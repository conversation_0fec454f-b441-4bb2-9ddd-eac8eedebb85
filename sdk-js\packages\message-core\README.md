# @availity/message-core

> A package wrapping the postMessage function with helper functions and security checks.

[![Version](https://img.shields.io/npm/v/@availity/message-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/message-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/message-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/message-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/message-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/message-core/package.json)

## Install

### NPM

```bash
npm install @availity/message-core
```

### Yarn

```bash
yarn add @availity/message-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/messaging)
