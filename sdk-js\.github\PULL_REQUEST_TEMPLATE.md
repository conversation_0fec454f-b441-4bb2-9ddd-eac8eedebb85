**Before submitting a pull request,** please make sure the following is done:

1. Fork [the repository](https://github.com/availity/availity-react) and create your branch from `master`.
2. Run `yarn` in the repository root.
3. If you've fixed a bug or added code that should be tested, add tests!
4. Ensure the test suite passes (`yarn test`). Tip: `yarn test --watch TestName` is helpful in development.
5. Make sure your code passed the conventional commits check. Read more about [conventional commits](https://www.conventionalcommits.org/en/v1.0.0-beta.4/#summary)
