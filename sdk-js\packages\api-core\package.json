{"name": "@availity/api-core", "version": "11.0.0", "description": "Base API definitions for the Availity REST API", "keywords": ["availity", "api", "http", "rest"], "bugs": "https://github.com/Availity/sdk-js/issues", "repository": "https://github.com/Availity/sdk-js", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"@availity/env-var": "workspace:*", "@availity/resolve-url": "workspace:*", "qs": "^6.14.0"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}