{"name": "@availity/dl-core", "version": "6.0.0", "description": "Utility to download files from services", "keywords": ["download", "file", "availity"], "homepage": "https://availity.github.io/sdk-js/api/downloads", "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/dl-core"}, "license": "MIT", "author": "<PERSON>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"@availity/api-core": "workspace:*", "js-file-download": "^0.4.12"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}