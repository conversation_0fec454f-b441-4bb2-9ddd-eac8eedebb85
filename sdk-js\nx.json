{"targetDefaults": {"build": {"cache": true}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^default", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.yaml", "{workspaceRoot}/tools/eslint-rules/**/*"]}}, "extends": "@nx/workspace/presets/npm.json", "targetDependencies": {"build": [{"target": "build", "projects": "dependencies"}], "prepare": [{"target": "prepare", "projects": "dependencies"}], "package": [{"target": "package", "projects": "dependencies"}]}, "pluginsConfig": {"@nx/js": {"analyzeSourceFiles": true}}, "useInferencePlugins": false, "defaultBase": "master"}