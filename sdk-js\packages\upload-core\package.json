{"name": "@availity/upload-core", "version": "8.0.0", "description": "Wrapper for tus-js-client", "keywords": ["tus", "resumable", "upload", "availity"], "homepage": "https://availity.github.io/sdk-js/api/uploads", "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/upload-core"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public", "publish:canary": "yarn npm publish --access public --tag canary"}, "dependencies": {"@availity/resolve-url": "workspace:*", "tus-js-client": "4.3.1"}, "devDependencies": {"msw": "2.3.5", "tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}