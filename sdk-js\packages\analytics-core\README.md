# @availity/analytics-core

> analytics package for tracking user behavior on the dom

[![Version](https://img.shields.io/npm/v/@availity/analytics-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/analytics-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/analytics-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/analytics-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/analytics-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/analytics-core/package.json)

## Install

### NPM

```bash
npm install @availity/analytics-core
```

### Yarn

```bash
yarn add @availity/analytics-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/analytics)
