# @availity/yup

> Method extensions for [yup](https://github.com/jquense/yup)

[![Version](https://img.shields.io/npm/v/@availity/yup.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/yup)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/yup.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/yup)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/yup?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/yup/package.json)

## Install

### NPM

```bash
npm install @availity/yup yup
```

### Yarn

```bash
yarn add @availity/yup yup
```

> You will need to also install [moment](https://www.npmjs.com/package/moment) if you plan on using the `date` or `dateRange` schemas

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/yup)
