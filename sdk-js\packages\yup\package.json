{"name": "@availity/yup", "version": "7.0.0", "description": "Additional methods for yup validation library", "keywords": ["yup", "availity", "validation"], "homepage": "https://availity.github.io/sdk-js/resources/yup", "bugs": {"url": "https://github.com/Availity/sdk-js/issues"}, "repository": {"type": "git", "url": "https://github.com/Availity/sdk-js.git", "directory": "packages/yup"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"lodash": "^4.17.21", "yup": "^0.32.11"}, "devDependencies": {"moment": "^2.30.1", "tsup": "^8.4.0", "typescript": "^5.5.4"}, "peerDependencies": {"moment": "^2.30.1"}, "publishConfig": {"access": "public"}}