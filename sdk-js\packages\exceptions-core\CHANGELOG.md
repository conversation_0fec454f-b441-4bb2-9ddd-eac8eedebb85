# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [6.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@5.0.3...@availity/exceptions-core@6.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [5.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@5.0.2...@availity/exceptions-core@5.0.3) (2025-03-14)



## [5.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@5.0.1...@availity/exceptions-core@5.0.2) (2025-03-10)



## [5.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@5.0.0...@availity/exceptions-core@5.0.1) (2024-10-14)



# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.1.4...@availity/exceptions-core@5.0.0) (2024-07-29)


### chore

* **exceptions-core:** upgrade to node 18 and 20 ([f3a3a69](https://github.com/Availity/sdk-js/commit/f3a3a69b0e3653a98c284f6c1906f9a180d3aad7))


### BREAKING CHANGES

* **exceptions-core:** drop support for node 14 and 16



## [4.1.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.1.3...@availity/exceptions-core@4.1.4) (2024-07-29)



## [4.1.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.1.2...@availity/exceptions-core@4.1.3) (2024-05-30)



## [4.1.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.1.1...@availity/exceptions-core@4.1.2) (2024-02-19)



## [4.1.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.1.0...@availity/exceptions-core@4.1.1) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/3217d96c1ad7b3b9b752d9376b88d9b91daabca6))
* allow exceptions-core to be published ([0c15350](https://github.com/Availity/sdk-js/commit/0c15350fa842360ae55df0a67d80b81aadbe5b9f))
* fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f486e49eb969320edfbfab422d47abe4ab1))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **exception-core:** use stacktrace-js for better stacktraces ([e59fd00](https://github.com/Availity/sdk-js/commit/e59fd004d7e70c829ca336c7eb4d20e95ff585bc))
* **exceptions-core:** adding message blacklist for exception logging SHI-3127 ([d3ee2e6](https://github.com/Availity/sdk-js/commit/d3ee2e622641d1532914d5aaa32dc5c0803aa4e7))
* **expections-axios:** add basic axios wrapper for execptions ([61803a9](https://github.com/Availity/sdk-js/commit/61803a910e5cf83db1a086af3d9c19656f7d75cb))


### BREAKING CHANGES

* Drop Internet Explorer support
* upgrades other packages that are using old package-locks



# [4.1.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.0.3...@availity/exceptions-core@4.1.0) (2023-02-10)


### Features

* **exceptions-core:** adding message blacklist for exception logging SHI-3127 ([d3ee2e6](https://github.com/Availity/sdk-js/commit/d3ee2e622641d1532914d5aaa32dc5c0803aa4e7))



## [4.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.0.2...@availity/exceptions-core@4.0.3) (2022-06-15)



## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.0.1...@availity/exceptions-core@4.0.2) (2022-05-24)



## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@4.0.0...@availity/exceptions-core@4.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.23...@availity/exceptions-core@4.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## 3.0.23 (2021-12-21)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.22 (2021-10-29)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.21 (2021-05-17)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.20 (2020-06-22)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.19 (2020-05-01)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.18 (2020-04-30)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.17 (2020-04-22)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.16 (2020-04-08)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.15 (2020-04-06)

**Note:** Version bump only for package @availity/exceptions-core





## 3.0.14 (2020-04-06)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.13](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.10...@availity/exceptions-core@3.0.13) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.12](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.11...@availity/exceptions-core@3.0.12) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.11](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.9...@availity/exceptions-core@3.0.11) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.10](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.8...@availity/exceptions-core@3.0.10) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [3.0.9](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.8...@availity/exceptions-core@3.0.9) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [3.0.8](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.5...@availity/exceptions-core@3.0.8) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.7](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.6...@availity/exceptions-core@3.0.7) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.6](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.5...@availity/exceptions-core@3.0.6) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-core





## [3.0.5](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.4...@availity/exceptions-core@3.0.5) (2020-01-03)

**Note:** Version bump only for package @availity/exceptions-core

## [3.0.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.3...@availity/exceptions-core@3.0.4) (2020-01-03)

**Note:** Version bump only for package @availity/exceptions-core

## [3.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.2...@availity/exceptions-core@3.0.3) (2019-09-27)

**Note:** Version bump only for package @availity/exceptions-core

## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.1...@availity/exceptions-core@3.0.2) (2019-04-23)

**Note:** Version bump only for package @availity/exceptions-core

## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@3.0.0...@availity/exceptions-core@3.0.1) (2019-04-17)

**Note:** Version bump only for package @availity/exceptions-core

# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@2.6.1...@availity/exceptions-core@3.0.0) (2019-03-18)

### Bug Fixes

-   add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/3217d96))
-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

# [2.7.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@2.6.1...@availity/exceptions-core@2.7.0) (2019-03-04)

### Bug Fixes

-   add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/3217d96))
-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

## [2.6.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-core@2.6.0...@availity/exceptions-core@2.6.1) (2019-02-12)

**Note:** Version bump only for package @availity/exceptions-core

# 2.6.0 (2019-02-12)

# 2.5.0 (2018-08-20)

# 2.4.0 (2018-07-12)

# 2.3.0 (2018-06-29)

### Features

-   **expections-axios:** add basic axios wrapper for execptions ([61803a9](https://github.com/Availity/sdk-js/commit/61803a9))

## 2.2.1 (2018-06-29)

### Bug Fixes

-   allow exceptions-core to be published ([0c15350](https://github.com/Availity/sdk-js/commit/0c15350))

# 2.2.0 (2018-06-29)

### Features

-   **exception-core:** use stacktrace-js for better stacktraces ([e59fd00](https://github.com/Availity/sdk-js/commit/e59fd00))

## 1.0.1 (2018-03-26)

# 1.0.0-alpha.17 (2018-01-18)

# 1.0.0-alpha.16 (2018-01-17)

# 1.0.0-alpha.15 (2018-01-12)

# 1.0.0-alpha.14 (2018-01-11)

# 1.0.0-alpha.13 (2018-01-10)

# 1.0.0-alpha.12 (2018-01-09)

# 1.0.0-alpha.11 (2018-01-06)

# 1.0.0-alpha.10 (2018-01-04)

# 1.0.0-alpha.9 (2018-01-03)

# 1.0.0-alpha.8 (2018-01-03)

# 1.0.0-alpha.7 (2018-01-03)

# 1.0.0-alpha.6 (2017-12-20)

# 1.0.0-alpha.5 (2017-12-20)

# 1.0.0-alpha.4 (2017-12-20)

# 1.0.0-alpha.3 (2017-12-19)

# 1.0.0-alpha.2 (2017-12-19)

# 1.0.0-alpha.1 (2017-12-19)

# 1.0.0-alpha.0 (2017-12-05)
