# @availity/authorizations-core

> package providing a base authorizations class to help check which permissions a user has.

[![Version](https://img.shields.io/npm/v/@availity/authorizations-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/authorizations-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/authorizations-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/authorizations-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/authorizations-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/authorizations-core/package.json)

## Install

### NPM

```bash
npm install @availity/authorizations-core
```

### Yarn

```bash
yarn add @availity/authorizations-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/api/authorizations)
