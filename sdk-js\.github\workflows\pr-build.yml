# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Pull Request Build

on:
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
        node: [20, 22]

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v3
        with:
          main-branch-name: "master"

      - name: Set Node Version
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: "yarn"
          cache-dependency-path: "yarn.lock"

      # - name: Get yarn cache directory path
      #   id: yarn-cache-dir-path
      #   run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      # - name: Restore yarn cache
      #   uses: actions/cache@v3
      #   id: yarn-cache
      #   with:
      #     path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
      #     key: ${{ runner.os }}-${{ matrix.node }}-yarn-${{ hashFiles('yarn.lock') }}
      #     restore-keys: |
      #       ${{ runner.os }}-${{ matrix.node }}-yarn-

      # - name: Artifactory Check
      #   run: yarn check:registry

      - name: Install Dependencies
        run: yarn install --immutable

      - name: Lint
        run: yarn nx affected --target=lint

      - name: Unit Test
        run: yarn test:ci

      - name: Publish Dry Run
        run: yarn publish:dry-run

      - name: Upload coverage report
        if: ${{ github.actor != 'dependabot[bot]' }}
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/coverage-final.json
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Build Packages
        run: yarn build:packages

      - name: Build Docs
        if: ${{matrix.node > 20}}
        run: yarn build:docs
