# @availity/api-core

> Base API definitions for the Availity REST API.

[![Version](https://img.shields.io/npm/v/@availity/api-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/api-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/api-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/api-core)

## Important Note

This package is now considered deprecated. It has been combined with [@availity/api-axios](../api-axios/README.md) as of `version 6`. You can safely upgrade to `@availity/api-axios` and remove the use of this package.

## Install

### NPM

```bash
npm install @availity/api-core
```

### Yarn

```bash
yarn add @availity/api-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/api/getting-started)
