{"name": "api-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/api-core"], "options": {"jestConfig": "packages/api-core/jest.config.js"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "angular", "commitMessageFormat": "chore({projectName}): release version ${version} [skip ci]", "tagPrefix": "@availity/{projectName}@", "baseBranch": "master"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"eslintConfig": ".eslintrc.yaml", "silent": false, "fix": false, "cache": true, "cacheLocation": "./node_modules/.cache/api-core/.eslintcache", "maxWarnings": -1, "quiet": false, "noEslintrc": false, "hasTypeAwareRules": true, "cacheStrategy": "metadata"}}}}