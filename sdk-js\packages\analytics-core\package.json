{"name": "@availity/analytics-core", "version": "6.0.2", "description": "Analytics base configuration for sdk-js", "keywords": ["availity", "analytics"], "homepage": "https://availity.github.io/sdk-js/resources/analytics", "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/analytics-core"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"uuid": "^10.0.0", "yup": "^0.32.11"}, "devDependencies": {"@availity/api-axios": "workspace:*", "tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}