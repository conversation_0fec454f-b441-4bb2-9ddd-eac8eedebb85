{"name": "@availity/exceptions-core", "version": "6.0.0", "description": "Availity class to log exceptions", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "devDependencies": {"mockdate": "^3.0.5", "stacktrace-js": "^2.0.2", "tsup": "^8.4.0", "typescript": "^5.5.4"}, "peerDependencies": {"stacktrace-js": "^2.0.2"}, "publishConfig": {"access": "public"}}