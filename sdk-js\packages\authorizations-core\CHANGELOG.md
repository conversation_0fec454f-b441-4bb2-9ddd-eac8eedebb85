# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@4.0.2...@availity/authorizations-core@5.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@4.0.1...@availity/authorizations-core@4.0.2) (2025-03-14)



## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@4.0.0...@availity/authorizations-core@4.0.1) (2025-03-10)



# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.7...@availity/authorizations-core@4.0.0) (2024-07-29)


### chore

* **authorizations-core:** upgrade to node 18 and 20 ([0db6c1a](https://github.com/Availity/sdk-js/commit/0db6c1a79cdd3c4e5670aa413414d7f343770eed))


### BREAKING CHANGES

* **authorizations-core:** drop support for node 14 and 16



## [3.0.7](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.6...@availity/authorizations-core@3.0.7) (2024-07-29)



## [3.0.6](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.5...@availity/authorizations-core@3.0.6) (2024-05-30)



## [3.0.5](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.4...@availity/authorizations-core@3.0.5) (2024-02-19)



## [3.0.4](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.3...@availity/authorizations-core@3.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* **authorizations-core:** swap array.prototype.find for array.prototype.filter ([43b1b4a](https://github.com/Availity/sdk-js/commit/43b1b4adee6125800f8cd328ce5fbf594e8f9bd8))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support
* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of



## [3.0.3](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.2...@availity/authorizations-core@3.0.3) (2022-06-15)



## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.1...@availity/authorizations-core@3.0.2) (2022-05-24)



## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@3.0.0...@availity/authorizations-core@3.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@2.0.2...@availity/authorizations-core@3.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## 2.0.2 (2021-12-21)

**Note:** Version bump only for package @availity/authorizations-core





## 2.0.1 (2021-10-29)

**Note:** Version bump only for package @availity/authorizations-core





# 2.0.0 (2021-05-17)


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


### BREAKING CHANGES

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of





## 1.0.22 (2020-06-22)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.21 (2020-05-01)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.20 (2020-04-30)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.19 (2020-04-22)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.18 (2020-04-08)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.17 (2020-04-06)

**Note:** Version bump only for package @availity/authorizations-core





## 1.0.16 (2020-04-06)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.15](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.12...@availity/authorizations-core@1.0.15) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.14](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.13...@availity/authorizations-core@1.0.14) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.13](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.11...@availity/authorizations-core@1.0.13) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.12](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.10...@availity/authorizations-core@1.0.12) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.0.11](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.10...@availity/authorizations-core@1.0.11) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.0.10](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.7...@availity/authorizations-core@1.0.10) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.9](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.8...@availity/authorizations-core@1.0.9) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.8](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.7...@availity/authorizations-core@1.0.8) (2020-02-13)

**Note:** Version bump only for package @availity/authorizations-core





## [1.0.7](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.6...@availity/authorizations-core@1.0.7) (2020-01-03)

**Note:** Version bump only for package @availity/authorizations-core

## [1.0.6](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.5...@availity/authorizations-core@1.0.6) (2020-01-03)

**Note:** Version bump only for package @availity/authorizations-core

## [1.0.5](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.4...@availity/authorizations-core@1.0.5) (2019-04-17)

**Note:** Version bump only for package @availity/authorizations-core

## [1.0.4](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.3...@availity/authorizations-core@1.0.4) (2019-03-18)

### Bug Fixes

-   **authorizations-core:** swap array.prototype.find for array.prototype.filter ([43b1b4a](https://github.com/Availity/sdk-js/commit/43b1b4a))

## [1.0.3](https://github.com/Availity/sdk-js/compare/@availity/authorizations-core@1.0.2...@availity/authorizations-core@1.0.3) (2019-02-12)

**Note:** Version bump only for package @availity/authorizations-core

## 1.0.2 (2019-02-12)

## 1.0.1 (2018-03-26)

# 1.0.0-alpha.17 (2018-01-18)

# 1.0.0-alpha.16 (2018-01-17)

# 1.0.0-alpha.15 (2018-01-12)

# 1.0.0-alpha.14 (2018-01-11)

# 1.0.0-alpha.13 (2018-01-10)

# 1.0.0-alpha.12 (2018-01-09)

# 1.0.0-alpha.11 (2018-01-06)

# 1.0.0-alpha.10 (2018-01-04)

# 1.0.0-alpha.9 (2018-01-03)

# 1.0.0-alpha.8 (2018-01-03)

# 1.0.0-alpha.7 (2018-01-03)

# 1.0.0-alpha.6 (2017-12-20)

# 1.0.0-alpha.5 (2017-12-20)

# 1.0.0-alpha.4 (2017-12-20)

# 1.0.0-alpha.3 (2017-12-19)

# 1.0.0-alpha.2 (2017-12-19)

# 1.0.0-alpha.1 (2017-12-19)

# 1.0.0-alpha.0 (2017-12-05)

**Note:** Version bump only for package @availity/authorizations-core
