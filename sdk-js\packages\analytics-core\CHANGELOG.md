# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [6.0.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@6.0.1...@availity/analytics-core@6.0.2) (2025-07-24)

### Dependency Updates

* `api-axios` updated to version `6.0.1`


## [6.0.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@6.0.0...@availity/analytics-core@6.0.1) (2025-06-13)

### Dependency Updates

* `api-axios` updated to version `6.0.0`


# [6.0.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.12...@availity/analytics-core@6.0.0) (2025-05-14)

### Dependency Updates

* `api-axios` updated to version `5.0.12`

### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [5.0.12](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.11...@availity/analytics-core@5.0.12) (2025-05-09)

### Dependency Updates

* `api-axios` updated to version `5.0.11`


## [5.0.11](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.10...@availity/analytics-core@5.0.11) (2025-04-09)

### Dependency Updates

* `api-axios` updated to version `5.0.10`


## [5.0.10](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.9...@availity/analytics-core@5.0.10) (2025-03-14)

### Dependency Updates

* `api-axios` updated to version `5.0.9`


## [5.0.9](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.8...@availity/analytics-core@5.0.9) (2025-03-10)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.8`


## [5.0.8](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.7...@availity/analytics-core@5.0.8) (2025-02-10)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.7`


## [5.0.7](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.6...@availity/analytics-core@5.0.7) (2025-02-04)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.6`


## [5.0.6](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.5...@availity/analytics-core@5.0.6) (2024-12-16)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.5`


## [5.0.5](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.4...@availity/analytics-core@5.0.5) (2024-12-13)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.4`


## [5.0.4](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.3...@availity/analytics-core@5.0.4) (2024-10-23)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.3`


## [5.0.3](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.2...@availity/analytics-core@5.0.3) (2024-10-14)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.2`


## [5.0.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.1...@availity/analytics-core@5.0.2) (2024-10-04)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.1`


## [5.0.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@5.0.0...@availity/analytics-core@5.0.1) (2024-09-19)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.0`


# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.1.3...@availity/analytics-core@5.0.0) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `4.1.3`

### chore

* **analytics-core:** upgrade to node 18 and 20 ([41125bf](https://github.com/Availity/sdk-js/commit/41125bf7f01156c06992af65b3841eceafc40a24))


### BREAKING CHANGES

* **analytics-core:** drop support for node 14 and 16



## [4.1.3](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.1.2...@availity/analytics-core@4.1.3) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `4.1.2`


## [4.1.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.1.1...@availity/analytics-core@4.1.2) (2024-05-30)



## [4.1.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.1.0...@availity/analytics-core@4.1.1) (2024-05-30)


### Bug Fixes

* **analytics-core:** add owner to request ([833aecc](https://github.com/Availity/sdk-js/commit/833aecc3096a69f24f17e207964209f6a103586e))
* **analytics-core:** update tests and types ([16e8f74](https://github.com/Availity/sdk-js/commit/16e8f74a32d497f466f88d9457114d939529c3d5))



# [4.1.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.5...@availity/analytics-core@4.1.0) (2024-03-07)


### Features

* **analytics-core:** add telemetry plugin ([5c5dfa1](https://github.com/Availity/sdk-js/commit/5c5dfa13e47813a6b5cfaa37caa70ebe744591bb))



## [4.0.5](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.4...@availity/analytics-core@4.0.5) (2024-02-19)



## [4.0.4](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.3...@availity/analytics-core@4.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* **analytics-core:** added dynamic length of attr key for slicing ([18df607](https://github.com/Availity/sdk-js/commit/18df6076fc38c2f5ab368e8415a5ee531bfde7e0))
* **analytics-core:** fix non-click events ([fce9b26](https://github.com/Availity/sdk-js/commit/fce9b269534d490e29d3a06d4751b2ce27b0833f))
* **analytics-core:** fixed function call not found ([2a7af7e](https://github.com/Availity/sdk-js/commit/2a7af7edc37b0e24adf2b2e2506414e3c7ea4cd4))
* **analytics-core:** moved extra assignments out ([23130f1](https://github.com/Availity/sdk-js/commit/23130f186cdecee22595cb0abe31d293cbb03328))
* **analytics-core:** recursive getComposedPath calls should be spread ([e39d180](https://github.com/Availity/sdk-js/commit/e39d180d7a0a348b52367bea8a87e6e9b0a98252))
* **analytics-core:** remove camelcase dep ([fc1be33](https://github.com/Availity/sdk-js/commit/fc1be33682c1de4befc46fa9692816fa5782aa5f))
* **analytics-core:** revert analytics invalid event method ([e823d2e](https://github.com/Availity/sdk-js/commit/e823d2e92b153bff64e73a45618f26cfe1385e8e))
* **analytics-core:** update types and error msg ([8c03422](https://github.com/Availity/sdk-js/commit/8c03422b8ced4c60738616905ce98b1523663f83))


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **analytics-core:** add autotrack ([849909e](https://github.com/Availity/sdk-js/commit/849909e60bc2b7dd521d072029f7d8a6f3653eae))
* **analytics-core:** add types and tests for util ([62cd19b](https://github.com/Availity/sdk-js/commit/62cd19b5c09cfc74830cae236bb48e18acf9fd42))
* **analytics-core:** added config object with attribute prefix and recursive ([4b5b9fa](https://github.com/Availity/sdk-js/commit/4b5b9fa0d76bdab1e55d2f18c788bcbbfc6338bc))
* **analytics-core:** added dma logging service ([18ee923](https://github.com/Availity/sdk-js/commit/18ee923f022caebb81cf9db3c32f016c7420af2f))
* **analytics-core:** adding dynamic event modifiers ([4d54f43](https://github.com/Availity/sdk-js/commit/4d54f4364ce37437b1ba9186c2e365e8b27e8cd5))
* **docs:** init commit getting things setup ([f525ff4](https://github.com/Availity/sdk-js/commit/f525ff407f5c807c2aafa4c7dadca1d43034d408))


### BREAKING CHANGES

* Drop Internet Explorer support
* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of



## [4.0.3](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.2...@availity/analytics-core@4.0.3) (2022-06-15)



## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.1...@availity/analytics-core@4.0.2) (2022-05-24)



## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@4.0.0...@availity/analytics-core@4.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@3.1.8...@availity/analytics-core@4.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## [3.1.8](https://github.com/availity/sdk-js/compare/@availity/analytics-core@3.1.6...@availity/analytics-core@3.1.8) (2022-02-22)

**Note:** Version bump only for package @availity/analytics-core





## [3.1.7](https://github.com/availity/sdk-js/compare/@availity/analytics-core@3.1.6...@availity/analytics-core@3.1.7) (2022-01-19)

**Note:** Version bump only for package @availity/analytics-core





## [3.1.6](https://github.com/availity/sdk-js/compare/@availity/analytics-core@3.1.5...@availity/analytics-core@3.1.6) (2021-12-21)

**Note:** Version bump only for package @availity/analytics-core





## 3.1.5 (2021-12-01)


### Bug Fixes

* **analytics-core:** recursive getComposedPath calls should be spread ([e39d180](https://github.com/Availity/sdk-js/commit/e39d180d7a0a348b52367bea8a87e6e9b0a98252))





## 3.1.4 (2021-11-16)


### Bug Fixes

* **analytics-core:** update types and error msg ([8c03422](https://github.com/Availity/sdk-js/commit/8c03422b8ced4c60738616905ce98b1523663f83))





## [3.1.3](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@3.1.2...@availity/analytics-core@3.1.3) (2021-10-29)

**Note:** Version bump only for package @availity/analytics-core





## [3.1.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@3.1.1...@availity/analytics-core@3.1.2) (2021-10-22)

**Note:** Version bump only for package @availity/analytics-core





## [3.1.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@3.1.0...@availity/analytics-core@3.1.1) (2021-10-20)

**Note:** Version bump only for package @availity/analytics-core





# 3.1.0 (2021-10-19)


### Features

* **analytics-core:** add types and tests for util ([62cd19b](https://github.com/Availity/sdk-js/commit/62cd19b5c09cfc74830cae236bb48e18acf9fd42))





# 3.0.0 (2021-05-17)


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


### BREAKING CHANGES

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of





## 2.8.23 (2020-06-22)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.22](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.21...@availity/analytics-core@2.8.22) (2020-05-01)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.21](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.20...@availity/analytics-core@2.8.21) (2020-05-01)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.20 (2020-05-01)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.19 (2020-04-30)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.18 (2020-04-22)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.17 (2020-04-08)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.16 (2020-04-06)

**Note:** Version bump only for package @availity/analytics-core





## 2.8.15 (2020-04-06)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.14](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.11...@availity/analytics-core@2.8.14) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.13](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.12...@availity/analytics-core@2.8.13) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.12](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.10...@availity/analytics-core@2.8.12) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.11](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.9...@availity/analytics-core@2.8.11) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.10](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.9...@availity/analytics-core@2.8.10) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.9](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.6...@availity/analytics-core@2.8.9) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.8](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.7...@availity/analytics-core@2.8.8) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.7](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.6...@availity/analytics-core@2.8.7) (2020-02-13)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.6](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.5...@availity/analytics-core@2.8.6) (2020-02-12)

**Note:** Version bump only for package @availity/analytics-core





## [2.8.5](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.4...@availity/analytics-core@2.8.5) (2020-01-14)

**Note:** Version bump only for package @availity/analytics-core

## [2.8.4](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.3...@availity/analytics-core@2.8.4) (2020-01-03)

**Note:** Version bump only for package @availity/analytics-core

## [2.8.3](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.2...@availity/analytics-core@2.8.3) (2020-01-03)

**Note:** Version bump only for package @availity/analytics-core

## [2.8.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.1...@availity/analytics-core@2.8.2) (2019-11-23)

**Note:** Version bump only for package @availity/analytics-core

## [2.8.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.8.0...@availity/analytics-core@2.8.1) (2019-11-01)

**Note:** Version bump only for package @availity/analytics-core

# [2.8.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.7.2...@availity/analytics-core@2.8.0) (2019-10-18)

### Features

-   **docs:** init commit getting things setup ([f525ff4](https://github.com/Availity/sdk-js/commit/f525ff4))

## [2.7.2](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.7.1...@availity/analytics-core@2.7.2) (2019-06-10)

**Note:** Version bump only for package @availity/analytics-core

## [2.7.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.7.0...@availity/analytics-core@2.7.1) (2019-04-17)

### Bug Fixes

-   **analytics-core:** revert analytics invalid event method ([e823d2e](https://github.com/Availity/sdk-js/commit/e823d2e))

# [2.7.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.6.1...@availity/analytics-core@2.7.0) (2019-04-17)

### Features

-   **analytics-core:** added dma logging service ([18ee923](https://github.com/Availity/sdk-js/commit/18ee923))

## [2.6.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.6.0...@availity/analytics-core@2.6.1) (2019-04-17)

### Bug Fixes

-   **analytics-core:** fixed function call not found ([2a7af7e](https://github.com/Availity/sdk-js/commit/2a7af7e))

# [2.6.0](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.5.1...@availity/analytics-core@2.6.0) (2019-03-18)

### Bug Fixes

-   **analytics-core:** added dynamic length of attr key for slicing ([18df607](https://github.com/Availity/sdk-js/commit/18df607))
-   **analytics-core:** moved extra assignments out ([23130f1](https://github.com/Availity/sdk-js/commit/23130f1))

### Features

-   **analytics-core:** added config object with attribute prefix and recursive ([4b5b9fa](https://github.com/Availity/sdk-js/commit/4b5b9fa))

## [2.5.1](https://github.com/Availity/sdk-js/compare/@availity/analytics-core@2.5.0...@availity/analytics-core@2.5.1) (2019-02-12)

**Note:** Version bump only for package @availity/analytics-core

# 2.5.0 (2019-02-12)

## 2.4.6 (2018-08-02)

### Bug Fixes

-   **analytics-core:** fix non-click events ([fce9b26](https://github.com/Availity/sdk-js/commit/fce9b26))

## 2.4.4 (2018-07-25)

### Bug Fixes

-   **analytics-core:** remove camelcase dep ([fc1be33](https://github.com/Availity/sdk-js/commit/fc1be33))

# 2.4.0 (2018-07-12)

### Features

-   **analytics-core:** add autotrack ([849909e](https://github.com/Availity/sdk-js/commit/849909e))

## 1.0.1 (2018-03-26)

# 1.0.0-alpha.17 (2018-01-18)

# 1.0.0-alpha.16 (2018-01-17)

# 1.0.0-alpha.15 (2018-01-12)

# 1.0.0-alpha.14 (2018-01-11)

# 1.0.0-alpha.13 (2018-01-10)

# 1.0.0-alpha.12 (2018-01-09)

# 1.0.0-alpha.11 (2018-01-06)

# 1.0.0-alpha.10 (2018-01-04)

# 1.0.0-alpha.9 (2018-01-03)

# 1.0.0-alpha.8 (2018-01-03)

# 1.0.0-alpha.7 (2018-01-03)

# 1.0.0-alpha.6 (2017-12-20)

# 1.0.0-alpha.5 (2017-12-20)

# 1.0.0-alpha.4 (2017-12-20)

# 1.0.0-alpha.3 (2017-12-19)

# 1.0.0-alpha.2 (2017-12-19)

# 1.0.0-alpha.1 (2017-12-19)

# 1.0.0-alpha.0 (2017-12-05)
