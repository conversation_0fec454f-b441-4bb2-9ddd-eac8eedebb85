#!/usr/bin/env ts-node

/**
 * Availity API Authentication Smoke Test
 * 
 * This TypeScript program performs an authentication smoke test using the @availity/api-axios library
 * with OAuth2 client credentials flow and HIPAA scope.
 * 
 * Requirements:
 * - Uses OAuth2 client credentials flow with HIPAA scope
 * - Validates authentication token is successfully obtained
 * - Verifies token has correct HIPAA scope
 * - Establishes basic API connectivity
 * - Includes proper error handling and logging
 */

import axios, { AxiosResponse, AxiosError } from 'axios';
import AvApi from '@availity/api-axios';

// Environment configuration
interface AuthConfig {
  clientId: string;
  clientSecret: string;
  tokenEndpoint: string;
  apiBaseUrl: string;
  scope: string;
}

interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

interface SmokeTestResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

class AvailityAuthSmokeTest {
  private config: AuthConfig;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor() {
    // Load configuration from environment variables or use defaults
    this.config = {
      clientId: process.env.AVAILITY_CLIENT_ID || '',
      clientSecret: process.env.AVAILITY_CLIENT_SECRET || '',
      tokenEndpoint: process.env.AVAILITY_TOKEN_ENDPOINT || 'https://api.availity.com/availity/v1/token',
      apiBaseUrl: process.env.AVAILITY_API_BASE_URL || 'https://api.availity.com',
      scope: 'hipaa'
    };

    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.config.clientId) {
      throw new Error('AVAILITY_CLIENT_ID environment variable is required');
    }
    if (!this.config.clientSecret) {
      throw new Error('AVAILITY_CLIENT_SECRET environment variable is required');
    }
  }

  private log(level: 'INFO' | 'ERROR' | 'SUCCESS' | 'WARN', message: string, details?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}`;
    
    console.log(logMessage);
    if (details) {
      console.log('Details:', JSON.stringify(details, null, 2));
    }
  }

  /**
   * Authenticate using OAuth2 client credentials flow
   */
  private async authenticate(): Promise<SmokeTestResult> {
    try {
      this.log('INFO', 'Starting OAuth2 authentication with client credentials flow');

      const requestBody = new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: this.config.scope
      });

      const response: AxiosResponse<TokenResponse> = await axios.post(
        this.config.tokenEndpoint,
        requestBody.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        }
      );

      if (response.status === 200 && response.data.access_token) {
        this.accessToken = response.data.access_token;
        this.tokenExpiry = new Date(Date.now() + (response.data.expires_in * 1000));

        this.log('SUCCESS', 'OAuth2 authentication successful', {
          tokenType: response.data.token_type,
          expiresIn: response.data.expires_in,
          scope: response.data.scope,
          tokenLength: this.accessToken.length
        });

        return {
          success: true,
          message: 'Authentication successful',
          details: {
            tokenType: response.data.token_type,
            expiresIn: response.data.expires_in,
            scope: response.data.scope,
            tokenExpiry: this.tokenExpiry.toISOString()
          },
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('Invalid response from token endpoint');
      }

    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response?.data || axiosError.message || 'Unknown authentication error';
      
      this.log('ERROR', 'Authentication failed', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        error: errorMessage
      });

      return {
        success: false,
        message: 'Authentication failed',
        details: { error: errorMessage },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Verify the token has the correct HIPAA scope
   */
  private async verifyTokenScope(): Promise<SmokeTestResult> {
    try {
      this.log('INFO', 'Verifying token scope');

      if (!this.accessToken) {
        throw new Error('No access token available for scope verification');
      }

      // Make a test request to verify the token works and has HIPAA scope
      const testResponse = await axios.get(
        `${this.config.apiBaseUrl}/availity/v1/coverages`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Accept': 'application/json'
          },
          timeout: 10000,
          params: {
            limit: 1 // Minimal request to test connectivity
          }
        }
      );

      if (testResponse.status === 200) {
        this.log('SUCCESS', 'Token scope verification successful', {
          status: testResponse.status,
          hasData: !!testResponse.data
        });

        return {
          success: true,
          message: 'Token scope verification successful - HIPAA access confirmed',
          details: {
            status: testResponse.status,
            endpoint: 'coverages',
            hasData: !!testResponse.data
          },
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error(`Unexpected response status: ${testResponse.status}`);
      }

    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response?.data || axiosError.message || 'Unknown scope verification error';
      
      this.log('ERROR', 'Token scope verification failed', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        error: errorMessage
      });

      return {
        success: false,
        message: 'Token scope verification failed',
        details: { error: errorMessage },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test basic API connectivity using @availity/api-axios
   */
  private async testApiConnectivity(): Promise<SmokeTestResult> {
    try {
      this.log('INFO', 'Testing API connectivity using @availity/api-axios');

      if (!this.accessToken) {
        throw new Error('No access token available for API connectivity test');
      }

      // Create an AvApi instance with custom configuration
      const api = new AvApi({
        name: 'coverages',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        },
        timeout: 10000
      });

      // Make a simple query to test connectivity
      // Use type assertion to work around strict TypeScript definitions in the library
      // This is a known issue with the @availity/api-axios type definitions
      const response = await api.query({ params: { limit: 1 } } as any);

      this.log('SUCCESS', 'API connectivity test successful', {
        hasResponse: !!response,
        hasData: !!response?.data
      });

      return {
        success: true,
        message: 'API connectivity test successful',
        details: {
          library: '@availity/api-axios',
          endpoint: 'coverages',
          hasResponse: !!response,
          hasData: !!response?.data
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown API connectivity error';

      this.log('ERROR', 'API connectivity test failed', { error: errorMessage });

      return {
        success: false,
        message: 'API connectivity test failed',
        details: { error: errorMessage },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Run the complete smoke test suite
   */
  public async runSmokeTest(): Promise<void> {
    this.log('INFO', '🚀 Starting Availity API Authentication Smoke Test');
    this.log('INFO', 'Configuration:', {
      clientId: this.config.clientId.substring(0, 8) + '...',
      tokenEndpoint: this.config.tokenEndpoint,
      apiBaseUrl: this.config.apiBaseUrl,
      scope: this.config.scope
    });

    const results: SmokeTestResult[] = [];

    // Step 1: Authenticate
    const authResult = await this.authenticate();
    results.push(authResult);

    if (!authResult.success) {
      this.log('ERROR', '❌ Smoke test failed at authentication step');
      this.printSummary(results);
      process.exit(1);
    }

    // Step 2: Verify token scope
    const scopeResult = await this.verifyTokenScope();
    results.push(scopeResult);

    if (!scopeResult.success) {
      this.log('ERROR', '❌ Smoke test failed at token scope verification step');
      this.printSummary(results);
      process.exit(1);
    }

    // Step 3: Test API connectivity
    const connectivityResult = await this.testApiConnectivity();
    results.push(connectivityResult);

    if (!connectivityResult.success) {
      this.log('ERROR', '❌ Smoke test failed at API connectivity test step');
      this.printSummary(results);
      process.exit(1);
    }

    // All tests passed
    this.log('SUCCESS', '✅ All smoke tests passed successfully!');
    this.printSummary(results);
  }

  private printSummary(results: SmokeTestResult[]): void {
    console.log('\n' + '='.repeat(60));
    console.log('SMOKE TEST SUMMARY');
    console.log('='.repeat(60));
    
    results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${status} - ${result.message}`);
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log('='.repeat(60));
    console.log(`OVERALL RESULT: ${passedTests}/${totalTests} tests passed`);
    console.log('='.repeat(60) + '\n');
  }
}

// Main execution
async function main(): Promise<void> {
  try {
    const smokeTest = new AvailityAuthSmokeTest();
    await smokeTest.runSmokeTest();
  } catch (error) {
    console.error('❌ Smoke test execution failed:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// Run the smoke test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { AvailityAuthSmokeTest, SmokeTestResult };
