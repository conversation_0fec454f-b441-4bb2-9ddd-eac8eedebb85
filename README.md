# Availity SDK Authentication Smoke Test

A comprehensive TypeScript program that performs an authentication smoke test using the `@availity/api-axios` library with OAuth2 client credentials flow and HIPAA scope.

## Features

- ✅ **OAuth2 Client Credentials Flow**: Implements proper OAuth2 authentication with client credentials
- ✅ **HIPAA Scope Validation**: Ensures the token has the correct HIPAA scope permissions
- ✅ **API Connectivity Testing**: Verifies basic API connectivity using the actual `@availity/api-axios` library
- ✅ **Comprehensive Error Handling**: Includes detailed error handling and logging
- ✅ **TypeScript Support**: Full TypeScript implementation with proper type definitions
- ✅ **Environment Configuration**: Configurable via environment variables
- ✅ **Detailed Logging**: Comprehensive logging for debugging and verification

## Prerequisites

- Node.js 20.x or 22.x
- npm or yarn package manager
- Valid Availity Client ID and Client Secret
- Application registered and subscribed to "Healthcare HIPAA Transactions (1.0.0)" API product

## Installation

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Set up environment variables:**
   Create a `.env` file or set the following environment variables:

   ```bash
   export AVAILITY_CLIENT_ID="your_client_id_here"
   export AVAILITY_CLIENT_SECRET="your_client_secret_here"
   ```

   Optional environment variables:

   ```bash
   export AVAILITY_TOKEN_ENDPOINT="https://api.availity.com/availity/v1/token"
   export AVAILITY_API_BASE_URL="https://api.availity.com"
   ```

## Usage

### Run the Smoke Test

```bash
npm test
```

Or using ts-node directly:

```bash
npx ts-node availity-auth-smoke-test.ts
```

### Development Mode (faster compilation)

```bash
npm run test:dev
```

### Build TypeScript

```bash
npm run build
```

## Test Verification

The smoke test performs the following verification steps:

### 1. **Authentication Setup**

- Uses OAuth2 client credentials flow
- Requests token with HIPAA scope
- Validates successful token retrieval
- Verifies token expiration time

### 2. **Token Scope Verification**

- Confirms the token has the correct HIPAA scope
- Makes a test API call to verify permissions
- Validates response from HIPAA-protected endpoint

### 3. **API Connectivity Test**

- Uses the actual `@availity/api-axios` library implementation
- Tests basic API connectivity
- Verifies the library works with the obtained token

## Expected Output

### Successful Test Run

```plaintext
[2024-01-XX] [INFO] 🚀 Starting Availity API Authentication Smoke Test
[2024-01-XX] [INFO] Starting OAuth2 authentication with client credentials flow
[2024-01-XX] [SUCCESS] OAuth2 authentication successful
[2024-01-XX] [INFO] Verifying token scope
[2024-01-XX] [SUCCESS] Token scope verification successful
[2024-01-XX] [INFO] Testing API connectivity using @availity/api-axios
[2024-01-XX] [SUCCESS] API connectivity test successful
[2024-01-XX] [SUCCESS] ✅ All smoke tests passed successfully!

============================================================
SMOKE TEST SUMMARY
============================================================
1. ✅ PASS - Authentication successful
2. ✅ PASS - Token scope verification successful - HIPAA access confirmed
3. ✅ PASS - API connectivity test successful
============================================================
OVERALL RESULT: 3/3 tests passed
============================================================
```

### Failed Test Run

```plaintext
[2024-01-XX] [ERROR] Authentication failed
Details: {
  "status": 401,
  "error": "Invalid client credentials"
}
[2024-01-XX] [ERROR] ❌ Smoke test failed at authentication step
```

## Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `AVAILITY_CLIENT_ID` | ✅ Yes | - | Your Availity application client ID |
| `AVAILITY_CLIENT_SECRET` | ✅ Yes | - | Your Availity application client secret |
| `AVAILITY_TOKEN_ENDPOINT` | No | `https://api.availity.com/availity/v1/token` | OAuth2 token endpoint |
| `AVAILITY_API_BASE_URL` | No | `https://api.availity.com` | Base URL for Availity APIs |

### Obtaining Credentials

1. **Create Account**: Register at [Availity Developer Portal](https://developer.availity.com/partner/)
2. **Register Application**: Create a new application to get Client ID and Client Secret
3. **Subscribe to API Product**: Subscribe to "Healthcare HIPAA Transactions (1.0.0)" with Demo or Standard plan
4. **Note Credentials**: Save your Client ID and Client Secret securely

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Verify Client ID and Client Secret are correct
   - Ensure application is registered and active
   - Check that credentials are properly set in environment variables

2. **403 Forbidden**
   - Verify application is subscribed to HIPAA Transactions API product
   - Check that subscription is active and not expired
   - Ensure proper scope permissions

3. **429 Too Many Requests**
   - Demo plan: Limited to 5 requests/second, 500/day
   - Standard plan: Limited to 100 requests/second, 100,000/day
   - Wait and retry if limits are exceeded

4. **Network/Timeout Errors**
   - Check internet connectivity
   - Verify API endpoints are accessible
   - Consider firewall or proxy settings

5. **TypeScript Compilation Issues**
   - The smoke test uses `as any` type assertion for the query method due to strict type definitions in the library
   - This is a known issue with the `@availity/api-axios` type definitions and does not affect functionality
   - The code will compile and run correctly despite the type assertion

### Debug Mode

For additional debugging information, you can modify the logging level in the source code or add additional console.log statements.

## Dependencies

- **@availity/api-axios**: ^11.1.1 - Availity's axios wrapper library
- **axios**: ^1.7.7 - HTTP client for making API requests
- **typescript**: ^5.5.4 - TypeScript compiler
- **ts-node**: ^10.9.2 - TypeScript execution environment

## License

MIT License - See LICENSE file for details

## Support

For issues related to:

- **Availity APIs**: Contact Availity Client Services at 800-282-4548
- **This smoke test**: Create an issue in the repository
- **SDK Documentation**: Visit [Availity SDK Documentation](https://availity.github.io/sdk-js/)
