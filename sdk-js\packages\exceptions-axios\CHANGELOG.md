# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [6.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@6.0.1...@availity/exceptions-axios@6.0.2) (2025-07-24)

### Dependency Updates

* `api-axios` updated to version `6.0.1`


## [6.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@6.0.0...@availity/exceptions-axios@6.0.1) (2025-06-13)

### Dependency Updates

* `api-axios` updated to version `6.0.0`


# [6.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.12...@availity/exceptions-axios@6.0.0) (2025-05-14)

### Dependency Updates

* `api-axios` updated to version `5.0.12`
* `exceptions-core` updated to version `5.0.12`

### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [5.0.12](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.11...@availity/exceptions-axios@5.0.12) (2025-05-09)

### Dependency Updates

* `api-axios` updated to version `5.0.11`


## [5.0.11](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.10...@availity/exceptions-axios@5.0.11) (2025-04-09)

### Dependency Updates

* `api-axios` updated to version `5.0.10`


## [5.0.10](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.9...@availity/exceptions-axios@5.0.10) (2025-03-14)

### Dependency Updates

* `api-axios` updated to version `5.0.9`
* `exceptions-core` updated to version `5.0.9`


## [5.0.9](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.8...@availity/exceptions-axios@5.0.9) (2025-03-10)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.8`
* `@availity/exceptions-core` updated to version `5.0.8`


## [5.0.8](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.7...@availity/exceptions-axios@5.0.8) (2025-02-10)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.7`


## [5.0.7](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.6...@availity/exceptions-axios@5.0.7) (2025-02-04)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.6`


## [5.0.6](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.5...@availity/exceptions-axios@5.0.6) (2024-12-16)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.5`


## [5.0.5](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.4...@availity/exceptions-axios@5.0.5) (2024-12-13)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.4`


## [5.0.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.3...@availity/exceptions-axios@5.0.4) (2024-10-23)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.3`


## [5.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.2...@availity/exceptions-axios@5.0.3) (2024-10-14)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.2`
* `@availity/exceptions-core` updated to version `5.0.2`


## [5.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.1...@availity/exceptions-axios@5.0.2) (2024-10-04)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.1`


## [5.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@5.0.0...@availity/exceptions-axios@5.0.1) (2024-09-19)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.0`


# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.7...@availity/exceptions-axios@5.0.0) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `4.0.7`
* `@availity/exceptions-core` updated to version `4.0.7`

### chore

* **exceptions-axios:** upgrade to node 18 and 20 ([8fc337a](https://github.com/Availity/sdk-js/commit/8fc337a066ddeca2b07a15926aff0b4270227d79))


### BREAKING CHANGES

* **exceptions-axios:** drop support for node 14 and 16



## [4.0.7](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.6...@availity/exceptions-axios@4.0.7) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `4.0.6`
* `@availity/exceptions-core` updated to version `4.0.6`


## [4.0.6](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.5...@availity/exceptions-axios@4.0.6) (2024-05-30)

### Dependency Updates

* `api-axios` updated to version `4.0.5`
* `exceptions-core` updated to version `4.0.5`


## [4.0.5](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.4...@availity/exceptions-axios@4.0.5) (2024-02-19)



## [4.0.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.3...@availity/exceptions-axios@4.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6ff8db97b6e1d66454c93e7a33dfe95aee))
* fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f486e49eb969320edfbfab422d47abe4ab1))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **expections-axios:** add basic axios wrapper for execptions ([61803a9](https://github.com/Availity/sdk-js/commit/61803a910e5cf83db1a086af3d9c19656f7d75cb))


### BREAKING CHANGES

* Drop Internet Explorer support
* upgrades other packages that are using old package-locks



## [4.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.2...@availity/exceptions-axios@4.0.3) (2022-06-15)



## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.1...@availity/exceptions-axios@4.0.2) (2022-05-24)



## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@4.0.0...@availity/exceptions-axios@4.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.69...@availity/exceptions-axios@4.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## [3.0.69](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.67...@availity/exceptions-axios@3.0.69) (2022-02-22)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.68](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.67...@availity/exceptions-axios@3.0.68) (2022-01-19)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.67 (2021-12-21)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.66](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.65...@availity/exceptions-axios@3.0.66) (2021-10-29)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.65](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.64...@availity/exceptions-axios@3.0.65) (2021-10-22)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.64 (2021-10-20)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.63 (2021-10-19)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.62](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.61...@availity/exceptions-axios@3.0.62) (2021-09-30)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.61](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.60...@availity/exceptions-axios@3.0.61) (2021-09-28)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.60 (2021-09-16)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.59](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.58...@availity/exceptions-axios@3.0.59) (2021-05-25)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.58](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.57...@availity/exceptions-axios@3.0.58) (2021-05-20)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.57](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.56...@availity/exceptions-axios@3.0.57) (2021-05-17)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.56 (2021-04-01)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.55 (2020-12-16)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.54](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.53...@availity/exceptions-axios@3.0.54) (2020-08-24)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.53](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.52...@availity/exceptions-axios@3.0.53) (2020-07-08)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.52](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.51...@availity/exceptions-axios@3.0.52) (2020-06-22)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.51](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.50...@availity/exceptions-axios@3.0.51) (2020-06-15)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.50](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.49...@availity/exceptions-axios@3.0.50) (2020-06-08)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.49](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.48...@availity/exceptions-axios@3.0.49) (2020-06-04)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.48](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.47...@availity/exceptions-axios@3.0.48) (2020-06-04)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.47 (2020-06-04)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.46 (2020-06-03)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.45](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.44...@availity/exceptions-axios@3.0.45) (2020-05-11)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.44 (2020-05-01)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.43 (2020-04-30)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.42 (2020-04-22)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.41 (2020-04-08)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.40 (2020-04-06)

**Note:** Version bump only for package @availity/exceptions-axios





## 3.0.39 (2020-04-06)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.38](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.37...@availity/exceptions-axios@3.0.38) (2020-03-25)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.37](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.36...@availity/exceptions-axios@3.0.37) (2020-03-06)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.36](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.35...@availity/exceptions-axios@3.0.36) (2020-03-04)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.35](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.34...@availity/exceptions-axios@3.0.35) (2020-03-02)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.34](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.33...@availity/exceptions-axios@3.0.34) (2020-02-18)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.33](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.30...@availity/exceptions-axios@3.0.33) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.32](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.31...@availity/exceptions-axios@3.0.32) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.31](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.29...@availity/exceptions-axios@3.0.31) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.30](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.28...@availity/exceptions-axios@3.0.30) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [3.0.29](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.28...@availity/exceptions-axios@3.0.29) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [3.0.28](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.25...@availity/exceptions-axios@3.0.28) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.27](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.26...@availity/exceptions-axios@3.0.27) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.26](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.25...@availity/exceptions-axios@3.0.26) (2020-02-13)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.25](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.24...@availity/exceptions-axios@3.0.25) (2020-02-12)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.24](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.23...@availity/exceptions-axios@3.0.24) (2020-01-28)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.23](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.22...@availity/exceptions-axios@3.0.23) (2020-01-23)

**Note:** Version bump only for package @availity/exceptions-axios





## [3.0.22](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.21...@availity/exceptions-axios@3.0.22) (2020-01-14)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.21](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.20...@availity/exceptions-axios@3.0.21) (2020-01-06)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.20](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.19...@availity/exceptions-axios@3.0.20) (2020-01-03)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.19](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.18...@availity/exceptions-axios@3.0.19) (2020-01-03)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.18](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.17...@availity/exceptions-axios@3.0.18) (2019-12-03)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.17](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.16...@availity/exceptions-axios@3.0.17) (2019-12-02)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.16](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.15...@availity/exceptions-axios@3.0.16) (2019-11-05)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.15](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.14...@availity/exceptions-axios@3.0.15) (2019-10-21)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.14](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.13...@availity/exceptions-axios@3.0.14) (2019-09-27)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.13](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.12...@availity/exceptions-axios@3.0.13) (2019-09-22)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.12](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.10...@availity/exceptions-axios@3.0.12) (2019-08-01)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.11](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.10...@availity/exceptions-axios@3.0.11) (2019-08-01)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.10](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.9...@availity/exceptions-axios@3.0.10) (2019-06-21)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.9](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.8...@availity/exceptions-axios@3.0.9) (2019-06-14)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.8](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.7...@availity/exceptions-axios@3.0.8) (2019-05-31)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.7](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.6...@availity/exceptions-axios@3.0.7) (2019-04-29)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.6](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.5...@availity/exceptions-axios@3.0.6) (2019-04-26)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.5](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.4...@availity/exceptions-axios@3.0.5) (2019-04-25)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.3...@availity/exceptions-axios@3.0.4) (2019-04-25)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.2...@availity/exceptions-axios@3.0.3) (2019-04-23)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.1...@availity/exceptions-axios@3.0.2) (2019-04-17)

**Note:** Version bump only for package @availity/exceptions-axios

## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@3.0.0...@availity/exceptions-axios@3.0.1) (2019-04-17)

**Note:** Version bump only for package @availity/exceptions-axios

# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.4...@availity/exceptions-axios@3.0.0) (2019-03-18)

### Bug Fixes

-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

# [2.8.0](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.4...@availity/exceptions-axios@2.8.0) (2019-03-04)

### Bug Fixes

-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

## [2.7.4](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.3...@availity/exceptions-axios@2.7.4) (2019-02-12)

**Note:** Version bump only for package @availity/exceptions-axios

## [2.7.3](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.2...@availity/exceptions-axios@2.7.3) (2019-02-12)

**Note:** Version bump only for package @availity/exceptions-axios

## [2.7.2](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.1...@availity/exceptions-axios@2.7.2) (2019-02-12)

**Note:** Version bump only for package @availity/exceptions-axios

<a name="2.7.1"></a>

## [2.7.1](https://github.com/Availity/sdk-js/compare/@availity/exceptions-axios@2.7.0...@availity/exceptions-axios@2.7.1) (2018-11-20)

**Note:** Version bump only for package @availity/exceptions-axios

<a name="2.7.0"></a>

# 2.7.0 (2018-11-20)

### Bug Fixes

-   **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6))

<a name="3.0.0"></a>

# 3.0.0 (2018-09-06)

<a name="2.6.0"></a>

# 2.6.0 (2018-08-23)

<a name="2.5.0"></a>

# 2.5.0 (2018-08-20)

<a name="2.4.8"></a>

## 2.4.8 (2018-08-06)

<a name="2.4.7"></a>

## 2.4.7 (2018-08-02)

<a name="2.4.6"></a>

## 2.4.6 (2018-08-02)

<a name="2.4.0"></a>

# 2.4.0 (2018-07-12)

<a name="2.3.3"></a>

## 2.3.3 (2018-07-12)

<a name="2.3.0"></a>

# 2.3.0 (2018-06-29)

### Features

-   **expections-axios:** add basic axios wrapper for execptions ([61803a9](https://github.com/Availity/sdk-js/commit/61803a9))
