---
name: "\U0001F680Feature request"
about: Suggest an idea or enhancement to our sdk.
---

## 🚀 Feature request

### Current Behavior

<!-- A clear and concise description of what is the current behavior / use.  -->

### Desired Behavior

<!-- A clear and concise description of what you want to happen.  -->

### Suggested Solution

<!-- Suggest a solution that the community/maintainers/you may take to enable the desired behavior  -->

<!-- NOTE: Feature Requests without suggested solutions may not be addressed or treated with the same level of urgency as those that have suggested solutions. -->

### Who does this impact? Who is this for?

<!-- Who is this for? All users? TypeScript users? Beginners? Advanced? Yourself? People using X, Y, X, etc.? -->

### Describe alternatives you've considered

<!-- A clear and concise description of any alternative solutions or features you've considered.  -->

### Additional context

<!-- Add any other context or links about the feature request here. -->
