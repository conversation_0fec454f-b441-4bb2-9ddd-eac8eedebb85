{"name": "@availity/dinosaurdocs", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "targets": {"version": {"executor": "@jscutlery/semver:version", "options": {"preset": "angular", "commitMessageFormat": "chore({projectName}): release version ${version} [skip ci]", "tagPrefix": "{projectName}@", "baseBranch": "master"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"eslintConfig": ".eslintrc.yaml", "silent": false, "fix": false, "cache": true, "cacheLocation": "./node_modules/.cache/docusaurus/.eslintcache", "maxWarnings": -1, "quiet": false, "noEslintrc": false, "hasTypeAwareRules": true, "cacheStrategy": "metadata"}}}}