# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/relay-id@3.0.2...@availity/relay-id@4.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/relay-id@3.0.1...@availity/relay-id@3.0.2) (2025-03-14)



## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/relay-id@3.0.0...@availity/relay-id@3.0.1) (2025-03-10)



# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.7...@availity/relay-id@3.0.0) (2024-07-29)


### chore

* **relay-id:** upgrade to node 18 and 20 ([430b30e](https://github.com/Availity/sdk-js/commit/430b30e20f7a5efa09a03315bcc1cf8426e23fc1))


### BREAKING CHANGES

* **relay-id:** drop support for node 14 and 16



## [2.0.7](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.6...@availity/relay-id@2.0.7) (2024-07-29)



## [2.0.6](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.5...@availity/relay-id@2.0.6) (2024-05-30)



## [2.0.5](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.4...@availity/relay-id@2.0.5) (2024-02-19)



## [2.0.4](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.3...@availity/relay-id@2.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **relay-id:** new small helper package to help deal with relay ids ([1336544](https://github.com/Availity/sdk-js/commit/13365441f1ddf9aa65e51cee2f8a90b0821e28ed))


### BREAKING CHANGES

* Drop Internet Explorer support



## [2.0.3](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.2...@availity/relay-id@2.0.3) (2022-06-15)



## [2.0.2](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.1...@availity/relay-id@2.0.2) (2022-05-24)



## [2.0.1](https://github.com/Availity/sdk-js/compare/@availity/relay-id@2.0.0...@availity/relay-id@2.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [2.0.0](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.17...@availity/relay-id@2.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## 1.1.17 (2021-12-21)

**Note:** Version bump only for package @availity/relay-id





## 1.1.16 (2021-10-22)

**Note:** Version bump only for package @availity/relay-id





## 1.1.15 (2021-05-17)

**Note:** Version bump only for package @availity/relay-id





## 1.1.14 (2020-05-01)

**Note:** Version bump only for package @availity/relay-id





## 1.1.13 (2020-04-30)

**Note:** Version bump only for package @availity/relay-id





## 1.1.12 (2020-04-22)

**Note:** Version bump only for package @availity/relay-id





## 1.1.11 (2020-04-08)

**Note:** Version bump only for package @availity/relay-id





## 1.1.10 (2020-04-06)

**Note:** Version bump only for package @availity/relay-id





## 1.1.9 (2020-04-06)

**Note:** Version bump only for package @availity/relay-id





## [1.1.8](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.5...@availity/relay-id@1.1.8) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





## [1.1.7](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.6...@availity/relay-id@1.1.7) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





## [1.1.6](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.4...@availity/relay-id@1.1.6) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





## [1.1.5](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.3...@availity/relay-id@1.1.5) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.1.4](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.3...@availity/relay-id@1.1.4) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.1.3](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.0...@availity/relay-id@1.1.3) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





## [1.1.2](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.1...@availity/relay-id@1.1.2) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





## [1.1.1](https://github.com/Availity/sdk-js/compare/@availity/relay-id@1.1.0...@availity/relay-id@1.1.1) (2020-02-13)

**Note:** Version bump only for package @availity/relay-id





# 1.1.0 (2020-02-04)


### Features

* **relay-id:** new small helper package to help deal with relay ids ([1336544](https://github.com/Availity/sdk-js/commit/1336544))
