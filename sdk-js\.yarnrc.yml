nodeLinker: node-modules

npmPublishRegistry: 'https://registry.npmjs.org/'

npmRegistryServer: 'https://registry.npmjs.org/'

plugins:
  - path: .yarn/plugins/@yarnpkg/plugin-interactive-tools.cjs
    spec: '@yarnpkg/plugin-interactive-tools'

yarnPath: .yarn/releases/yarn-3.2.0.cjs

supportedArchitectures:
  cpu:
    - current
    - x64
    - arm64
  libc:
    - current
    - musl
    - glibc
  os:
    - current
    - linux
    - darwin
    - win32
