# @availity/user-activity-broadcaster

> This package broadcasts user activity to the navigation.

[![Version](https://img.shields.io/npm/v/@availity/user-activity-broadcaster.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/user-activity-broadcaster)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/user-activity-broadcaster.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/user-activity-broadcaster)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/user-activity-broadcaster?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/user-activity-broadcaster/package.json)

## Installation

### NPM

```bash
npm install @availity/user-activity-broadcaster
```

### Yarn

```bash
yarn add @availity/user-activity-broadcaster
```

## Usage

> All you have to do is include this as a dependency and import it into the root of your web application.
