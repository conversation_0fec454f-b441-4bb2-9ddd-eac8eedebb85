# @availity/native-form

> Submit JSON data via a native form, not AJAX. Useful when you need to open a new page with a POST action.

[![Version](https://img.shields.io/npm/v/@availity/native-form.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/native-form)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/native-form.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/native-form)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/native-form?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/native-form/package.json)

## Install

### NPM

```bash
npm install @availity/native-form
```

### Yarn

```bash
yarn add @availity/native-form
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/native-form)
