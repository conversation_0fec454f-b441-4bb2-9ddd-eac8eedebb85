---
name: "\U0001F41B Bug report"
about: Open a new issue here if something isn't working as expected.
---

## 🐛 Bug report

### Current Behavior

<!-- If applicable, add screenshots to help explain your problem. -->

### Expected behavior

<!-- A clear and concise description of what you expected to happen. -->

### Reproducible example

<!-- Use one of the Codesandbox or Stackblitz: -->

### Suggested solution(s)

<!-- How could we solve this bug? What changes would need to made to the affected components? -->

### Additional context

<!-- Add any other context about the problem here.  -->

### Your environment

<!-- PLEASE FILL THIS OUT -->

```bash
npx envinfo --npmPackages '@availity/*' --binaries
```
