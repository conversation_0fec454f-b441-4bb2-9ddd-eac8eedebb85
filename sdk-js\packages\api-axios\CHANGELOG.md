# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [11.1.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@11.1.0...@availity/api-axios@11.1.1) (2025-07-24)



# [11.1.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@11.0.0...@availity/api-axios@11.1.0) (2025-06-13)


### Features

* add dma-cloud url api ([5f5a389](https://github.com/Availity/sdk-js/commit/5f5a389fd8a440cafb9479c8edec8bb7969b4181))



# [11.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@10.0.4...@availity/api-axios@11.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [10.0.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@10.0.3...@availity/api-axios@10.0.4) (2025-05-09)



## [10.0.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@10.0.2...@availity/api-axios@10.0.3) (2025-04-09)



## [10.0.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@10.0.1...@availity/api-axios@10.0.2) (2025-03-14)



## [10.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@10.0.0...@availity/api-axios@10.0.1) (2025-03-10)



# [10.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.7...@availity/api-axios@10.0.0) (2025-02-10)


### Features

* **upload-core:** convert to ts ([e8f2b6c](https://github.com/Availity/sdk-js/commit/e8f2b6c3b30aeec8c26d306035a876ddcc0202fc))


### BREAKING CHANGES

* **upload-core:** update to the latest version of tus-js-client



## [9.0.7](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.6...@availity/api-axios@9.0.7) (2025-02-04)


### Bug Fixes

* **api-axios:** allow absolute urls ([b85c82f](https://github.com/Availity/sdk-js/commit/b85c82f5e1d4fb3e70d49a3ed66d0bb5016df28b))



## [9.0.6](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.5...@availity/api-axios@9.0.6) (2024-12-16)


### Bug Fixes

* **api-axios:** all logic for limit ([e7e83fb](https://github.com/Availity/sdk-js/commit/e7e83fbf699fed973b3d04ef77d8775a5565fc21))



## [9.0.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.4...@availity/api-axios@9.0.5) (2024-12-13)


### Bug Fixes

* **api-axios:** all logic math.min ([a722c65](https://github.com/Availity/sdk-js/commit/a722c65b36b405382a0a1d1273b5e358f9f3d5e9))
* **api-axios:** default 1 if response.limit not present on all ([0f2ca5d](https://github.com/Availity/sdk-js/commit/0f2ca5d4c53eceb7d184b6c62a1295ece9436acf))
* **api-axios:** use request.length if response.limit not present on all ([bd7812d](https://github.com/Availity/sdk-js/commit/bd7812d9f30a561f15c5803b9a0b87b52326d51d))
* **api-axios:** use request.limit if response.limit not present on all ([0994453](https://github.com/Availity/sdk-js/commit/0994453ffdf54f4d3036d40fa5ceab1873a565b1))
* use request limit if response limit is not present for all ([2cef9cb](https://github.com/Availity/sdk-js/commit/2cef9cb64b0cc360cf146da7277e67b79f96cef5))



## [9.0.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.3...@availity/api-axios@9.0.4) (2024-10-23)


### Bug Fixes

* **api-axios:** update fileDelivery type ([e588ffd](https://github.com/Availity/sdk-js/commit/e588ffd58f8e7b4c010519094ea856825bb18ea2))



## [9.0.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.2...@availity/api-axios@9.0.3) (2024-10-14)



## [9.0.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.1...@availity/api-axios@9.0.2) (2024-10-04)



## [9.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@9.0.0...@availity/api-axios@9.0.1) (2024-09-19)


### Bug Fixes

* add axios filesDelivery location test ([c306e2a](https://github.com/Availity/sdk-js/commit/c306e2add2a79ada23c6daaa7400356175d41355))
* don't append FUD id twice ([d81520f](https://github.com/Availity/sdk-js/commit/d81520f4de32defbe7e466f61c6378f8014bcf0d))
* linting ([8339deb](https://github.com/Availity/sdk-js/commit/8339deb654059d9678296e4c6ae2596b5df0740e))



# [9.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.9...@availity/api-axios@9.0.0) (2024-07-29)


### chore

* **api-axios:** upgrade to node 18 and 20 ([9d8e02f](https://github.com/Availity/sdk-js/commit/9d8e02f89a6f4b563df24406220674d26c94b909))


### BREAKING CHANGES

* **api-axios:** drop support for node 14 and 16



## [8.0.9](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.8...@availity/api-axios@8.0.9) (2024-07-29)



## [8.0.8](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.7...@availity/api-axios@8.0.8) (2024-05-30)


### Bug Fixes

* add http option to types and convert interface to class ([e7b74c4](https://github.com/Availity/sdk-js/commit/e7b74c4b78a4367a6cdeae6c86ad5df585ab65f0))
* **api-axios:** update url for telemetry ([a5fabec](https://github.com/Availity/sdk-js/commit/a5fabec2e38dfdf7661ed47f4806dc56a6c2be1b))



## [8.0.7](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.6...@availity/api-axios@8.0.7) (2024-04-12)


### Bug Fixes

* **api-axios:** use newConfig.pollingMethod as polling method ([0a16d8c](https://github.com/Availity/sdk-js/commit/0a16d8c8583321beac58d3acc5fc8ac74b4d9990))



## [8.0.6](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.5...@availity/api-axios@8.0.6) (2024-03-21)


### Bug Fixes

* **api-axios:** use AvApi getLocation method in AvMicroserviceApi ([c3618bc](https://github.com/Availity/sdk-js/commit/c3618bcea650fde98a76c9ce30f453a60715f7bb))



## [8.0.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.4...@availity/api-axios@8.0.5) (2024-02-20)



## [8.0.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.3...@availity/api-axios@8.0.4) (2024-02-19)



## [8.0.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.2...@availity/api-axios@8.0.3) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/****************************************))
* api implementation peerDeps ([28c163d](https://github.com/Availity/sdk-js/commit/28c163dcd02ef8a4623c91c1231d4c6a8f07e7c3))
* **api-axios:** add avTelemetryApi types ([99f8ebf](https://github.com/Availity/sdk-js/commit/99f8ebf3e099b0a932bd13dd31c325c3beba2216))
* **api-axios:** change axios to peerDep ([9e0a9cd](https://github.com/Availity/sdk-js/commit/9e0a9cdb24e3764707a844a9a36a38a34bd162e8))
* **api-axios:** export telemetry types ([c2f9d89](https://github.com/Availity/sdk-js/commit/c2f9d894511d645ade86a663fe9f05af029c3ddf))
* **api-axios:** fix ms to extend core ms ([a2f65e5](https://github.com/Availity/sdk-js/commit/a2f65e563989f16690c1b65231245f88d0a7f4b3))
* **api-axios:** fix organizations resources check in axios ([36a1b9b](https://github.com/Availity/sdk-js/commit/36a1b9b91f26773f2e9ef096886276c9f3e47de7))
* **api-axios:** fixed dev dep getting removed ([365776a](https://github.com/Availity/sdk-js/commit/365776a8a7cd74982fc56f67f278d989c06431da))
* **api-axios:** had to update packge to require v4.0 of api-core ([c5160de](https://github.com/Availity/sdk-js/commit/c5160dede5ab26553ecec383d7c3dfd72be8bc1c))
* **api-axios:** make afterUpdate an arrow function ([9c8724c](https://github.com/Availity/sdk-js/commit/9c8724c1bd1efda2783fe97ac4b0482228e60807))
* **api-axios:** override getQueryResultKey for regions ([03f8b42](https://github.com/Availity/sdk-js/commit/03f8b42083e31ca3b75654f88bc1b324a4a29dbb))
* **api-axios:** remove resolveHost and change AvUsersApi back to AvUserApi ([1263cac](https://github.com/Availity/sdk-js/commit/1263cac6182cc70cdcda6a5cf3818cecb3c7bc95))
* **api-axios:** replace axios merge with deep-assign ([a7183b1](https://github.com/Availity/sdk-js/commit/a7183b10e000102b51208b3f4297a6b4ba3ff897))
* **api-axios:** resolve lint error ([62efcaf](https://github.com/Availity/sdk-js/commit/62efcaf010c65d3d2bf8b0004953608628c15465))
* **api-axios:** set the browser field ([9206790](https://github.com/Availity/sdk-js/commit/92067904e99d6d893db4d3b063da4bc47af22ad0))
* **api-axios:** update name of types ([c9bee17](https://github.com/Availity/sdk-js/commit/c9bee17647b5be953788c33d3770c7dc994784f1))
* **api-axios:** update permissions response type ([a6ef800](https://github.com/Availity/sdk-js/commit/a6ef8005eccb1278b9a1a826b5881c77ee585a91))
* **api-axios:** url creation was not working as expected ([630e4c7](https://github.com/Availity/sdk-js/commit/630e4c7d7395547e75f9396e038cca54dd2f1124))
* **api-axios:** us merge-options for merge ([25b75b4](https://github.com/Availity/sdk-js/commit/25b75b4657a80544e9e803117612a30bf6551b98))
* **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6ff8db97b6e1d66454c93e7a33dfe95aee))
* **api-core:** filesDelivery merge ([eb75160](https://github.com/Availity/sdk-js/commit/eb75160e7b07a43e858066f38ff8cef969391362))
* fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f486e49eb969320edfbfab422d47abe4ab1))
* import axios properly ([1c616dd](https://github.com/Availity/sdk-js/commit/1c616dd63e00733249498cc94ff526772270a906))


### chore

* **api-axios:** bump axios to v1 ([c45f2e5](https://github.com/Availity/sdk-js/commit/c45f2e519291c2ecf4687653a3a4c55e6169eeac))
* **api-core:** core, angular, axios api naming consistency ([f129fad](https://github.com/Availity/sdk-js/commit/f129fad36f4e4d8c81fd0b4989811846dd245ee3))


### Code Refactoring

* **api-core:** pass config object to core classes ([d7b859c](https://github.com/Availity/sdk-js/commit/d7b859c80837a8256bcf4538b30d042882db2640))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **analytics-core:** added dma logging service ([68aee41](https://github.com/Availity/sdk-js/commit/68aee41761e5a5960ee1e997e32e471aad455dc1))
* **api-axios:** add aries 2 pdf resource ([fbcfa65](https://github.com/Availity/sdk-js/commit/fbcfa65a6163a4775a9a9327b0a9c56fed5162ea))
* **api-axios:** add route configurations api ([f91c9aa](https://github.com/Availity/sdk-js/commit/f91c9aa13eba40af7af52f11393810b5daa49ad3))
* **api-axios:** add support for filtering organizations by permission and resource ([69b2a3a](https://github.com/Availity/sdk-js/commit/69b2a3a9b965fa00fe41aa0651365c59800e323d))
* **api-axios:** add telemetry resource DP-2591 ([57977ec](https://github.com/Availity/sdk-js/commit/57977ec062ef3fa0e8143a66e6564ceeea45cbf8))
* **api-axios:** add typescript support ([6d2d9ee](https://github.com/Availity/sdk-js/commit/6d2d9eee72a3aaa1cf2619179eddfb1da86dd82c))
* **api-axios:** move api-core code into api-axios ([aaa6b90](https://github.com/Availity/sdk-js/commit/aaa6b901838b216bcd1e0169594e5474cc7f57e7))
* **api-core:** add codes resource ([4ea7ba4](https://github.com/Availity/sdk-js/commit/4ea7ba41fcef02b67b2e4928b17a04eff2d8b9fd))
* **api-core:** add file upload delivery batch api ([eff0a3c](https://github.com/Availity/sdk-js/commit/eff0a3cff384d99d13d0fd0795e693eef5c51af2))
* **api-core:** add webQL resource ([67b9797](https://github.com/Availity/sdk-js/commit/67b9797718ac55d0a2a08d7e5e7513791dc82a28))
* **api-core:** added graphql resource for slotmachine ([356a686](https://github.com/Availity/sdk-js/commit/356a6868b24be6b5388000770d711bcce5e5fa00))
* **api-core:** added new resource for disclaimers ([b7de72e](https://github.com/Availity/sdk-js/commit/b7de72ed6d6c910a2571e18753bfee6a038837d7))
* **api-core:** helper function for settings api ([#64](https://github.com/Availity/sdk-js/issues/64)) ([f247235](https://github.com/Availity/sdk-js/commit/f2472358bfef0744ec31e7b764ae1586b314af6b))


### Reverts

* Revert "chore: testing build stuff" ([24cc807](https://github.com/Availity/sdk-js/commit/24cc807fa91236f38a82b17a006bbe88641af32a))


### BREAKING CHANGES

* **api-axios:** axios version 1.4.0 or greater is now required
* Drop Internet Explorer support
* **api-axios:** api-core no longer required, naming, args for constructor
* upgrades other packages that are using old package-locks
* **api-axios:** api-axios requires v4.0.0 of api-core
* **api-core:** export naming conventions have been refactored to match for each implementation (angular/axios). Constructors are prefixed with 'Av', implementations are prefixed with 'av', and Apis are postfixed with 'Api'.
* **api-core:** Options to core classes are being passed in as config object instead of parameters. 



## [8.0.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.1...@availity/api-axios@8.0.2) (2023-08-01)


### Bug Fixes

* **api-axios:** export telemetry types ([c2f9d89](https://github.com/Availity/sdk-js/commit/c2f9d894511d645ade86a663fe9f05af029c3ddf))



## [8.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@8.0.0...@availity/api-axios@8.0.1) (2023-08-01)


### Bug Fixes

* **api-axios:** add avTelemetryApi types ([99f8ebf](https://github.com/Availity/sdk-js/commit/99f8ebf3e099b0a932bd13dd31c325c3beba2216))



# [8.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.3.2...@availity/api-axios@8.0.0) (2023-06-14)


### chore

* **api-axios:** bump axios to v1 ([c45f2e5](https://github.com/Availity/sdk-js/commit/c45f2e519291c2ecf4687653a3a4c55e6169eeac))


### BREAKING CHANGES

* **api-axios:** axios version 1.4.0 or greater is now required



## [7.3.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.3.1...@availity/api-axios@7.3.2) (2023-02-22)


### Bug Fixes

* **api-axios:** override getQueryResultKey for regions ([03f8b42](https://github.com/Availity/sdk-js/commit/03f8b42083e31ca3b75654f88bc1b324a4a29dbb))



## [7.3.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.3.0...@availity/api-axios@7.3.1) (2022-12-20)


### Bug Fixes

* **api-axios:** make afterUpdate an arrow function ([9c8724c](https://github.com/Availity/sdk-js/commit/9c8724c1bd1efda2783fe97ac4b0482228e60807))



# [7.3.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.2.1...@availity/api-axios@7.3.0) (2022-10-26)


### Features

* **api-axios:** add aries 2 pdf resource ([fbcfa65](https://github.com/Availity/sdk-js/commit/fbcfa65a6163a4775a9a9327b0a9c56fed5162ea))



## [7.2.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.2.0...@availity/api-axios@7.2.1) (2022-09-21)



# [7.2.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.1.0...@availity/api-axios@7.2.0) (2022-09-21)


### Features

* **api-axios:** add route configurations api ([f91c9aa](https://github.com/Availity/sdk-js/commit/f91c9aa13eba40af7af52f11393810b5daa49ad3))



# [7.1.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.0.3...@availity/api-axios@7.1.0) (2022-09-21)


### Features

* **api-axios:** add telemetry resource DP-2591 ([57977ec](https://github.com/Availity/sdk-js/commit/57977ec062ef3fa0e8143a66e6564ceeea45cbf8))



## [7.0.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.0.2...@availity/api-axios@7.0.3) (2022-06-15)



## [7.0.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.0.1...@availity/api-axios@7.0.2) (2022-05-24)


### Reverts

* Revert "chore: testing build stuff" ([24cc807](https://github.com/Availity/sdk-js/commit/24cc807fa91236f38a82b17a006bbe88641af32a))



## [7.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@7.0.0...@availity/api-axios@7.0.1) (2022-04-28)


### Bug Fixes

* **api-axios:** set the browser field ([9206790](https://github.com/Availity/sdk-js/commit/92067904e99d6d893db4d3b063da4bc47af22ad0))



# [7.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@6.0.8...@availity/api-axios@7.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## [6.0.8](https://github.com/availity/sdk-js/compare/@availity/api-axios@6.0.6...@availity/api-axios@6.0.8) (2022-02-22)

**Note:** Version bump only for package @availity/api-axios





## [6.0.7](https://github.com/availity/sdk-js/compare/@availity/api-axios@6.0.6...@availity/api-axios@6.0.7) (2022-01-19)


### Bug Fixes

* **api-axios:** url creation was not working as expected ([630e4c7](https://github.com/availity/sdk-js/commit/630e4c7d7395547e75f9396e038cca54dd2f1124))





## 6.0.6 (2021-12-21)

**Note:** Version bump only for package @availity/api-axios





## [6.0.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@6.0.4...@availity/api-axios@6.0.5) (2021-10-29)

**Note:** Version bump only for package @availity/api-axios





## [6.0.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@6.0.3...@availity/api-axios@6.0.4) (2021-10-22)


### Bug Fixes

* **api-axios:** resolve lint error ([62efcaf](https://github.com/Availity/sdk-js/commit/62efcaf010c65d3d2bf8b0004953608628c15465))





## 6.0.3 (2021-10-20)


### Bug Fixes

* **api-axios:** update permissions response type ([a6ef800](https://github.com/Availity/sdk-js/commit/a6ef8005eccb1278b9a1a826b5881c77ee585a91))





## 6.0.2 (2021-10-19)

**Note:** Version bump only for package @availity/api-axios





## [6.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@6.0.0...@availity/api-axios@6.0.1) (2021-09-30)


### Bug Fixes

* **api-axios:** fix organizations resources check in axios ([36a1b9b](https://github.com/Availity/sdk-js/commit/36a1b9b91f26773f2e9ef096886276c9f3e47de7))





# [6.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.6.0...@availity/api-axios@6.0.0) (2021-09-28)


### Bug Fixes

* **api-axios:** change axios to peerDep ([9e0a9cd](https://github.com/Availity/sdk-js/commit/9e0a9cdb24e3764707a844a9a36a38a34bd162e8))
* **api-axios:** remove resolveHost and change AvUsersApi back to AvUserApi ([1263cac](https://github.com/Availity/sdk-js/commit/1263cac6182cc70cdcda6a5cf3818cecb3c7bc95))
* **api-axios:** update name of types ([c9bee17](https://github.com/Availity/sdk-js/commit/c9bee17647b5be953788c33d3770c7dc994784f1))


### Features

* **api-axios:** move api-core code into api-axios ([aaa6b90](https://github.com/Availity/sdk-js/commit/aaa6b901838b216bcd1e0169594e5474cc7f57e7))


### BREAKING CHANGES

* **api-axios:** api-core no longer required, naming, args for constructor





# 5.6.0 (2021-09-16)


### Features

* **api-axios:** add typescript support ([6d2d9ee](https://github.com/Availity/sdk-js/commit/6d2d9eee72a3aaa1cf2619179eddfb1da86dd82c))





## [5.5.14](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.13...@availity/api-axios@5.5.14) (2021-05-25)

**Note:** Version bump only for package @availity/api-axios





## [5.5.13](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.12...@availity/api-axios@5.5.13) (2021-05-20)

**Note:** Version bump only for package @availity/api-axios





## [5.5.12](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.11...@availity/api-axios@5.5.12) (2021-05-17)

**Note:** Version bump only for package @availity/api-axios





## 5.5.11 (2021-04-01)

**Note:** Version bump only for package @availity/api-axios





## 5.5.10 (2020-12-16)

**Note:** Version bump only for package @availity/api-axios





## [5.5.9](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.8...@availity/api-axios@5.5.9) (2020-08-24)

**Note:** Version bump only for package @availity/api-axios





## [5.5.8](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.7...@availity/api-axios@5.5.8) (2020-07-08)

**Note:** Version bump only for package @availity/api-axios





## [5.5.7](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.6...@availity/api-axios@5.5.7) (2020-06-22)

**Note:** Version bump only for package @availity/api-axios





## [5.5.6](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.5...@availity/api-axios@5.5.6) (2020-06-15)

**Note:** Version bump only for package @availity/api-axios





## [5.5.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.4...@availity/api-axios@5.5.5) (2020-06-08)

**Note:** Version bump only for package @availity/api-axios





## [5.5.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.3...@availity/api-axios@5.5.4) (2020-06-04)

**Note:** Version bump only for package @availity/api-axios





## [5.5.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.5.2...@availity/api-axios@5.5.3) (2020-06-04)

**Note:** Version bump only for package @availity/api-axios





## 5.5.2 (2020-06-04)

**Note:** Version bump only for package @availity/api-axios





## 5.5.1 (2020-06-03)

**Note:** Version bump only for package @availity/api-axios





# [5.5.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.32...@availity/api-axios@5.5.0) (2020-05-11)


### Features

* **api-axios:** add support for filtering organizations by permission and resource ([69b2a3a](https://github.com/Availity/sdk-js/commit/69b2a3a9b965fa00fe41aa0651365c59800e323d))





## 5.4.32 (2020-05-01)

**Note:** Version bump only for package @availity/api-axios





## 5.4.31 (2020-04-30)

**Note:** Version bump only for package @availity/api-axios





## 5.4.30 (2020-04-22)

**Note:** Version bump only for package @availity/api-axios





## 5.4.29 (2020-04-08)

**Note:** Version bump only for package @availity/api-axios





## 5.4.28 (2020-04-06)

**Note:** Version bump only for package @availity/api-axios





## 5.4.27 (2020-04-06)

**Note:** Version bump only for package @availity/api-axios





## [5.4.26](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.25...@availity/api-axios@5.4.26) (2020-03-25)

**Note:** Version bump only for package @availity/api-axios





## [5.4.25](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.24...@availity/api-axios@5.4.25) (2020-03-06)

**Note:** Version bump only for package @availity/api-axios





## [5.4.24](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.23...@availity/api-axios@5.4.24) (2020-03-04)

**Note:** Version bump only for package @availity/api-axios





## [5.4.23](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.22...@availity/api-axios@5.4.23) (2020-03-02)

**Note:** Version bump only for package @availity/api-axios





## [5.4.22](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.21...@availity/api-axios@5.4.22) (2020-02-18)

**Note:** Version bump only for package @availity/api-axios





## [5.4.21](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.18...@availity/api-axios@5.4.21) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.20](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.19...@availity/api-axios@5.4.20) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.19](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.17...@availity/api-axios@5.4.19) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.18](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.16...@availity/api-axios@5.4.18) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.17](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.16...@availity/api-axios@5.4.17) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.16](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.13...@availity/api-axios@5.4.16) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.15](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.14...@availity/api-axios@5.4.15) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.14](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.13...@availity/api-axios@5.4.14) (2020-02-13)

**Note:** Version bump only for package @availity/api-axios





## [5.4.13](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.12...@availity/api-axios@5.4.13) (2020-02-12)

**Note:** Version bump only for package @availity/api-axios





## [5.4.12](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.11...@availity/api-axios@5.4.12) (2020-01-28)

**Note:** Version bump only for package @availity/api-axios





## [5.4.11](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.10...@availity/api-axios@5.4.11) (2020-01-23)

**Note:** Version bump only for package @availity/api-axios





## [5.4.10](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.9...@availity/api-axios@5.4.10) (2020-01-14)

**Note:** Version bump only for package @availity/api-axios

## [5.4.9](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.8...@availity/api-axios@5.4.9) (2020-01-06)

**Note:** Version bump only for package @availity/api-axios

## [5.4.8](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.7...@availity/api-axios@5.4.8) (2020-01-03)

**Note:** Version bump only for package @availity/api-axios

## [5.4.7](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.6...@availity/api-axios@5.4.7) (2020-01-03)

**Note:** Version bump only for package @availity/api-axios

## [5.4.6](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.5...@availity/api-axios@5.4.6) (2019-12-03)

**Note:** Version bump only for package @availity/api-axios

## [5.4.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.4...@availity/api-axios@5.4.5) (2019-12-02)

**Note:** Version bump only for package @availity/api-axios

## [5.4.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.3...@availity/api-axios@5.4.4) (2019-11-05)

**Note:** Version bump only for package @availity/api-axios

## [5.4.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.2...@availity/api-axios@5.4.3) (2019-10-21)

**Note:** Version bump only for package @availity/api-axios

## [5.4.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.1...@availity/api-axios@5.4.2) (2019-09-27)

**Note:** Version bump only for package @availity/api-axios

## [5.4.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.4.0...@availity/api-axios@5.4.1) (2019-09-22)

**Note:** Version bump only for package @availity/api-axios

# [5.4.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.2.1...@availity/api-axios@5.4.0) (2019-08-01)

### Features

-   **api-core:** add webQL resource ([67b9797](https://github.com/Availity/sdk-js/commit/67b9797))

# [5.3.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.2.1...@availity/api-axios@5.3.0) (2019-08-01)

### Features

-   **api-core:** add webQL resource ([67b9797](https://github.com/Availity/sdk-js/commit/67b9797))

## [5.2.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.2.0...@availity/api-axios@5.2.1) (2019-06-21)

**Note:** Version bump only for package @availity/api-axios

# [5.2.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.6...@availity/api-axios@5.2.0) (2019-06-14)

### Features

-   **api-core:** add codes resource ([4ea7ba4](https://github.com/Availity/sdk-js/commit/4ea7ba4))

## [5.1.6](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.5...@availity/api-axios@5.1.6) (2019-05-31)

**Note:** Version bump only for package @availity/api-axios

## [5.1.5](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.4...@availity/api-axios@5.1.5) (2019-04-29)

**Note:** Version bump only for package @availity/api-axios

## [5.1.4](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.3...@availity/api-axios@5.1.4) (2019-04-26)

**Note:** Version bump only for package @availity/api-axios

## [5.1.3](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.2...@availity/api-axios@5.1.3) (2019-04-25)

**Note:** Version bump only for package @availity/api-axios

## [5.1.2](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.1...@availity/api-axios@5.1.2) (2019-04-25)

**Note:** Version bump only for package @availity/api-axios

## [5.1.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.1.0...@availity/api-axios@5.1.1) (2019-04-23)

**Note:** Version bump only for package @availity/api-axios

# [5.1.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.0.1...@availity/api-axios@5.1.0) (2019-04-17)

### Features

-   **analytics-core:** added dma logging service ([68aee41](https://github.com/Availity/sdk-js/commit/68aee41))

## [5.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@5.0.0...@availity/api-axios@5.0.1) (2019-04-17)

**Note:** Version bump only for package @availity/api-axios

# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@4.0.0...@availity/api-axios@5.0.0) (2019-03-18)

### Bug Fixes

-   **api-axios:** fixed dev dep getting removed ([365776a](https://github.com/Availity/sdk-js/commit/365776a))
-   add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/3217d96))
-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

# [4.1.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@4.0.0...@availity/api-axios@4.1.0) (2019-03-04)

### Bug Fixes

-   **api-axios:** fixed dev dep getting removed ([365776a](https://github.com/Availity/sdk-js/commit/365776a))
-   add lerna ignore for package-locks ([3217d96](https://github.com/Availity/sdk-js/commit/3217d96))
-   fixed package-lock issues boiling down to this repo ([8c896f4](https://github.com/Availity/sdk-js/commit/8c896f4))

### BREAKING CHANGES

-   upgrades other packages that are using old package-locks

# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@3.1.1...@availity/api-axios@4.0.0) (2019-02-12)

### Bug Fixes

-   **api-axios:** had to update packge to require v4.0 of api-core ([c5160de](https://github.com/Availity/sdk-js/commit/c5160de))

### BREAKING CHANGES

-   **api-axios:** api-axios requires v4.0.0 of api-core

## [3.1.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@3.1.0...@availity/api-axios@3.1.1) (2019-02-12)

**Note:** Version bump only for package @availity/api-axios

# [3.1.0](https://github.com/Availity/sdk-js/compare/@availity/api-axios@3.0.1...@availity/api-axios@3.1.0) (2019-02-12)

### Features

-   **api-core:** added graphql resource for slotmachine ([356a686](https://github.com/Availity/sdk-js/commit/356a686))
-   **api-core:** added new resource for disclaimers ([b7de72e](https://github.com/Availity/sdk-js/commit/b7de72e))

<a name="3.0.1"></a>

## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/api-axios@3.0.0...@availity/api-axios@3.0.1) (2018-11-20)

**Note:** Version bump only for package @availity/api-axios

<a name="3.0.0"></a>

# 3.0.0 (2018-11-20)

### Bug Fixes

-   **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6))
-   **api-core:** filesDelivery merge ([eb75160](https://github.com/Availity/sdk-js/commit/eb75160))

### Features

-   **api-core:** add file upload delivery batch api ([eff0a3c](https://github.com/Availity/sdk-js/commit/eff0a3c))

<a name="3.0.0"></a>

# 3.0.0 (2018-09-06)

### Bug Fixes

-   **api-axios:** us merge-options for merge ([25b75b4](https://github.com/Availity/sdk-js/commit/25b75b4))

<a name="2.6.0"></a>

# 2.6.0 (2018-08-23)

### Features

-   **api-core:** helper function for settings api ([#64](https://github.com/Availity/sdk-js/issues/64)) ([f247235](https://github.com/Availity/sdk-js/commit/f247235))

<a name="2.5.0"></a>

# 2.5.0 (2018-08-20)

<a name="2.4.8"></a>

## 2.4.8 (2018-08-06)

### Bug Fixes

-   **api-axios:** replace axios merge with deep-assign ([a7183b1](https://github.com/Availity/sdk-js/commit/a7183b1))

<a name="2.4.7"></a>

## 2.4.7 (2018-08-02)

<a name="2.4.6"></a>

## 2.4.6 (2018-08-02)

<a name="2.4.0"></a>

# 2.4.0 (2018-07-12)

<a name="2.3.3"></a>

## 2.3.3 (2018-07-12)

<a name="2.2.0"></a>

# 2.2.0 (2018-06-29)

<a name="2.1.2"></a>

## 2.1.2 (2018-06-13)

<a name="2.1.1"></a>

## 2.1.1 (2018-05-25)

<a name="2.1.0"></a>

# 2.1.0 (2018-05-24)

<a name="2.0.3"></a>

## 2.0.3 (2018-05-10)

<a name="2.0.2"></a>

## 2.0.2 (2018-05-04)

### Bug Fixes

-   api implementation peerDeps ([28c163d](https://github.com/Availity/sdk-js/commit/28c163d))

<a name="2.0.1"></a>

## 2.0.1 (2018-05-04)

<a name="2.0.0"></a>

# 2.0.0 (2018-05-04)

### Bug Fixes

-   **api-axios:** fix ms to extend core ms ([a2f65e5](https://github.com/Availity/sdk-js/commit/a2f65e5))

### Chores

-   **api-core:** core, angular, axios api naming consistency ([f129fad](https://github.com/Availity/sdk-js/commit/f129fad))

### BREAKING CHANGES

-   **api-core:** export naming conventions have been refactored to match for each implementation (angular/axios). Constructors are prefixed with 'Av', implementations are prefixed with 'av', and Apis are postfixed with 'Api'.

<a name="1.4.0"></a>

# 1.4.0 (2018-04-17)

<a name="1.2.0"></a>

# 1.2.0 (2018-04-12)

<a name="1.1.1"></a>

## 1.1.1 (2018-03-28)

<a name="1.0.1"></a>

## 1.0.1 (2018-03-26)

<a name="1.0.0-alpha.17"></a>

# 1.0.0-alpha.17 (2018-01-18)

<a name="1.0.0-alpha.16"></a>

# 1.0.0-alpha.16 (2018-01-17)

<a name="1.0.0-alpha.15"></a>

# 1.0.0-alpha.15 (2018-01-12)

<a name="1.0.0-alpha.14"></a>

# 1.0.0-alpha.14 (2018-01-11)

<a name="1.0.0-alpha.13"></a>

# 1.0.0-alpha.13 (2018-01-10)

<a name="1.0.0-alpha.12"></a>

# 1.0.0-alpha.12 (2018-01-09)

<a name="1.0.0-alpha.11"></a>

# 1.0.0-alpha.11 (2018-01-06)

### Code Refactoring

-   **api-core:** pass config object to core classes ([d7b859c](https://github.com/Availity/sdk-js/commit/d7b859c))

### BREAKING CHANGES

-   **api-core:** Options to core classes are being passed in as config object instead of parameters.

<a name="1.0.0-alpha.10"></a>

# 1.0.0-alpha.10 (2018-01-04)

<a name="1.0.0-alpha.9"></a>

# 1.0.0-alpha.9 (2018-01-03)

<a name="1.0.0-alpha.8"></a>

# 1.0.0-alpha.8 (2018-01-03)

<a name="1.0.0-alpha.7"></a>

# 1.0.0-alpha.7 (2018-01-03)

<a name="1.0.0-alpha.6"></a>

# 1.0.0-alpha.6 (2017-12-20)

<a name="1.0.0-alpha.5"></a>

# 1.0.0-alpha.5 (2017-12-20)

<a name="1.0.0-alpha.4"></a>

# 1.0.0-alpha.4 (2017-12-20)

<a name="1.0.0-alpha.3"></a>

# 1.0.0-alpha.3 (2017-12-19)

<a name="1.0.0-alpha.2"></a>

# 1.0.0-alpha.2 (2017-12-19)

<a name="1.0.0-alpha.1"></a>

# 1.0.0-alpha.1 (2017-12-19)

<a name="1.0.0-alpha.0"></a>

# 1.0.0-alpha.0 (2017-12-05)

### Bug Fixes

-   import axios properly ([1c616dd](https://github.com/Availity/sdk-js/commit/1c616dd))
