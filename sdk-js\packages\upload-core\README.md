# @availity/upload-core

> Wrapper for tus-js-client

[![Version](https://img.shields.io/npm/v/@availity/upload-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/upload-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/upload-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/upload-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/upload-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/upload-core/package.json)

## Install

### NPM

```bash
npm install @availity/upload-core
```

### Yarn

```bash
yarn add @availity/upload-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/api/uploads)
