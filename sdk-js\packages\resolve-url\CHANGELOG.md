# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@3.0.3...@availity/resolve-url@4.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [3.0.3](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@3.0.2...@availity/resolve-url@3.0.3) (2025-03-14)



## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@3.0.1...@availity/resolve-url@3.0.2) (2025-03-10)



## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@3.0.0...@availity/resolve-url@3.0.1) (2025-03-06)


### Bug Fixes

* **resolve-url:** add exports field ([ca4cb88](https://github.com/Availity/sdk-js/commit/ca4cb881b9e7f7773bb7295a67352f4c5efa7f42))



# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.7...@availity/resolve-url@3.0.0) (2024-07-29)


### chore

* **resolve-url:** upgrade to node 18 and 20 ([f5a6976](https://github.com/Availity/sdk-js/commit/f5a6976a2c44c0bbf01c7ed1b26d4f3140df56e7))


### BREAKING CHANGES

* **resolve-url:** drop support for node 14 and 16



## [2.0.7](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.6...@availity/resolve-url@2.0.7) (2024-07-29)



## [2.0.6](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.5...@availity/resolve-url@2.0.6) (2024-05-30)



## [2.0.5](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.4...@availity/resolve-url@2.0.5) (2024-02-19)



## [2.0.4](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.3...@availity/resolve-url@2.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* **resolve-url:** fix exported types ([0f7780e](https://github.com/Availity/sdk-js/commit/0f7780eca4f8a09b40029a77a74aa0b815d899a6))
* **resolve-url:** fixed import ([b5a8077](https://github.com/Availity/sdk-js/commit/b5a807736bde15744f399ea822797c541fef8880))
* **resolve-url:** fixes incorrect regex results failing tests, assign default param to baseIRI ([2aebb4b](https://github.com/Availity/sdk-js/commit/2aebb4b5ac512b8be1818a97914b84e6b734b1da))
* **resolve-url:** make ie11 compat ([c2e6e95](https://github.com/Availity/sdk-js/commit/c2e6e95c243c7ffbafb2ac2844bdf0733fc251c3))
* **resolve-url:** startsWith not supported in IE ([1a3d0b7](https://github.com/Availity/sdk-js/commit/1a3d0b77410a3f04cdd4763e5b227b6473ee4b88))
* **resolve-url:** use window.location.origin ([bcfe310](https://github.com/Availity/sdk-js/commit/bcfe310ece0ac23d4dde3cc68be927ac1f509438))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **resolve-url:** add isAbsoluteUrl to exports ([016cee6](https://github.com/Availity/sdk-js/commit/016cee64a805b01d674be3d79bb549e5905ca7c6))
* **resolve-url:** options to resolve url using base url ([2cd0670](https://github.com/Availity/sdk-js/commit/2cd0670384291f6fbdc5d3db3f93ab66ea1568c4))
* **resolve-url:** resolve relative urls to full location href ([f4ec953](https://github.com/Availity/sdk-js/commit/f4ec953c6e2f65f6a0790ec7ed073a4dbcd7e438))


### BREAKING CHANGES

* Drop Internet Explorer support



## [2.0.3](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.2...@availity/resolve-url@2.0.3) (2022-06-15)



## [2.0.2](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.1...@availity/resolve-url@2.0.2) (2022-05-24)



## [2.0.1](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@2.0.0...@availity/resolve-url@2.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [2.0.0](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.25...@availity/resolve-url@2.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## [1.1.25](https://github.com/availity/sdk-js/compare/@availity/resolve-url@1.1.24...@availity/resolve-url@1.1.25) (2022-02-22)


### Bug Fixes

* **resolve-url:** fix exported types ([0f7780e](https://github.com/availity/sdk-js/commit/0f7780eca4f8a09b40029a77a74aa0b815d899a6))





## 1.1.24 (2021-12-21)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.23](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.22...@availity/resolve-url@1.1.23) (2021-10-29)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.22 (2021-10-22)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.21 (2021-05-17)


### Bug Fixes

* **resolve-url:** fixes incorrect regex results failing tests, assign default param to baseIRI ([2aebb4b](https://github.com/Availity/sdk-js/commit/2aebb4b5ac512b8be1818a97914b84e6b734b1da))





## 1.1.20 (2020-06-22)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.19 (2020-05-01)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.18 (2020-04-30)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.17 (2020-04-22)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.16 (2020-04-08)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.15 (2020-04-06)

**Note:** Version bump only for package @availity/resolve-url





## 1.1.14 (2020-04-06)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.13](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.10...@availity/resolve-url@1.1.13) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.12](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.11...@availity/resolve-url@1.1.12) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.11](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.9...@availity/resolve-url@1.1.11) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.10](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.8...@availity/resolve-url@1.1.10) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.9](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.8...@availity/resolve-url@1.1.9) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.8](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.5...@availity/resolve-url@1.1.8) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.7](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.6...@availity/resolve-url@1.1.7) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.6](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.5...@availity/resolve-url@1.1.6) (2020-02-13)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.5](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.4...@availity/resolve-url@1.1.5) (2020-02-12)


### Bug Fixes

* **resolve-url:** fixed import ([b5a8077](https://github.com/Availity/sdk-js/commit/b5a807736bde15744f399ea822797c541fef8880))





## [1.1.4](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.3...@availity/resolve-url@1.1.4) (2020-01-23)

**Note:** Version bump only for package @availity/resolve-url





## [1.1.3](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.2...@availity/resolve-url@1.1.3) (2020-01-03)

**Note:** Version bump only for package @availity/resolve-url

## [1.1.2](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.1...@availity/resolve-url@1.1.2) (2020-01-03)

**Note:** Version bump only for package @availity/resolve-url

## [1.1.1](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.1.0...@availity/resolve-url@1.1.1) (2019-10-21)

**Note:** Version bump only for package @availity/resolve-url

# [1.1.0](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.0.3...@availity/resolve-url@1.1.0) (2019-04-29)

### Features

-   **resolve-url:** add isAbsoluteUrl to exports ([016cee6](https://github.com/Availity/sdk-js/commit/016cee6))

## [1.0.3](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.0.2...@availity/resolve-url@1.0.3) (2019-04-26)

### Bug Fixes

-   **resolve-url:** use window.location.origin ([bcfe310](https://github.com/Availity/sdk-js/commit/bcfe310))

## [1.0.2](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.0.1...@availity/resolve-url@1.0.2) (2019-04-25)

### Bug Fixes

-   **resolve-url:** startsWith not supported in IE ([1a3d0b7](https://github.com/Availity/sdk-js/commit/1a3d0b7))

## [1.0.1](https://github.com/Availity/sdk-js/compare/@availity/resolve-url@1.0.0...@availity/resolve-url@1.0.1) (2019-04-25)

### Bug Fixes

-   **resolve-url:** make ie11 compat ([c2e6e95](https://github.com/Availity/sdk-js/commit/c2e6e95))

# 1.0.0 (2019-04-23)

### Features

-   **resolve-url:** options to resolve url using base url ([2cd0670](https://github.com/Availity/sdk-js/commit/2cd0670))
-   **resolve-url:** resolve relative urls to full location href ([f4ec953](https://github.com/Availity/sdk-js/commit/f4ec953))
