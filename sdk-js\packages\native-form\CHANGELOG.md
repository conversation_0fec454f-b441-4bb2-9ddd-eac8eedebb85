# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [7.0.3](https://github.com/Availity/sdk-js/compare/@availity/native-form@7.0.2...@availity/native-form@7.0.3) (2025-07-24)

### Dependency Updates

* `api-axios` updated to version `7.0.2`


## [7.0.2](https://github.com/Availity/sdk-js/compare/@availity/native-form@7.0.1...@availity/native-form@7.0.2) (2025-06-13)

### Dependency Updates

* `api-axios` updated to version `7.0.1`


## [7.0.1](https://github.com/Availity/sdk-js/compare/@availity/native-form@7.0.0...@availity/native-form@7.0.1) (2025-06-10)


### Bug Fixes

* **native-form:** allow any sso type ([0106b8f](https://github.com/Availity/sdk-js/commit/0106b8f85f35e09114e5ebf414dd686e6275eda2))



# [7.0.0](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.12...@availity/native-form@7.0.0) (2025-05-14)

### Dependency Updates

* `api-axios` updated to version `6.0.12`

### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [6.0.12](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.11...@availity/native-form@6.0.12) (2025-05-09)

### Dependency Updates

* `api-axios` updated to version `6.0.11`


## [6.0.11](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.10...@availity/native-form@6.0.11) (2025-04-09)

### Dependency Updates

* `api-axios` updated to version `6.0.10`


## [6.0.10](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.9...@availity/native-form@6.0.10) (2025-03-14)

### Dependency Updates

* `api-axios` updated to version `6.0.9`


## [6.0.9](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.8...@availity/native-form@6.0.9) (2025-03-10)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.8`


## [6.0.8](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.7...@availity/native-form@6.0.8) (2025-02-10)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.7`


## [6.0.7](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.6...@availity/native-form@6.0.7) (2025-02-04)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.6`


## [6.0.6](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.5...@availity/native-form@6.0.6) (2024-12-16)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.5`


## [6.0.5](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.4...@availity/native-form@6.0.5) (2024-12-13)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.4`


## [6.0.4](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.3...@availity/native-form@6.0.4) (2024-10-23)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.3`


## [6.0.3](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.2...@availity/native-form@6.0.3) (2024-10-14)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.2`


## [6.0.2](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.1...@availity/native-form@6.0.2) (2024-10-04)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.1`


## [6.0.1](https://github.com/Availity/sdk-js/compare/@availity/native-form@6.0.0...@availity/native-form@6.0.1) (2024-09-19)

### Dependency Updates

* `@availity/api-axios` updated to version `6.0.0`


# [6.0.0](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.7...@availity/native-form@6.0.0) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.7`

### chore

* **native-form:** upgrade to node 18 and 20 ([4a88392](https://github.com/Availity/sdk-js/commit/4a88392f1643fc7cb847933823f821a80c7dfa92))


### BREAKING CHANGES

* **native-form:** drop support for node 14 and 16



## [5.0.7](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.6...@availity/native-form@5.0.7) (2024-07-29)

### Dependency Updates

* `@availity/api-axios` updated to version `5.0.6`


## [5.0.6](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.5...@availity/native-form@5.0.6) (2024-05-30)

### Dependency Updates

* `api-axios` updated to version `5.0.5`


## [5.0.5](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.4...@availity/native-form@5.0.5) (2024-02-19)



## [5.0.4](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.3...@availity/native-form@5.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* append function is not defined for ie and edge mobile ([d995343](https://github.com/Availity/sdk-js/commit/d995343c63484b5df1b0660d5a44e52db5c2c6a9))
* **native-form:** fixed error trying to call toString on undefined ([c009460](https://github.com/Availity/sdk-js/commit/c0094607e992ef7072eeb37dedfc908eb2911f3e))
* **native-form:** update types and use api-axiosv6 ([02b0cd6](https://github.com/Availity/sdk-js/commit/02b0cd6a6c1f401c71b4ddc8482297604531983a))
* **native-form:** updated handling of falsey values ([ab57615](https://github.com/Availity/sdk-js/commit/ab57615e2158b41a54132e92e05026a2cfd1e0ea))
* **native-form:** updated test suite name ([fbf824c](https://github.com/Availity/sdk-js/commit/fbf824c7f2c5540193ab3d244a9598524f44d443))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **native-form:** add native form ([#62](https://github.com/Availity/sdk-js/issues/62)) ([8aa0471](https://github.com/Availity/sdk-js/commit/8aa047123d240906c129466800102b9dff2139d5))
* **native-form:** add operationName to graphql request for query tracking ([5772f57](https://github.com/Availity/sdk-js/commit/5772f57f30278ede6d85daefe2ab7341e8b78b92))
* **native-form:** call avWebQLApi to obtain sso type ([23a1bd9](https://github.com/Availity/sdk-js/commit/23a1bd98cc71334454d137be822a5a5a23a32447))


### BREAKING CHANGES

* Drop Internet Explorer support
* **native-form:** native-form now exports an async function, no longer defaults type
MIGRATION:
 Before: nativeForm(...args);
 After: await nativeForm(...args);
* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of



## [5.0.3](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.2...@availity/native-form@5.0.3) (2022-06-15)



## [5.0.2](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.1...@availity/native-form@5.0.2) (2022-05-24)



## [5.0.1](https://github.com/Availity/sdk-js/compare/@availity/native-form@5.0.0...@availity/native-form@5.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.12...@availity/native-form@5.0.0) (2022-04-28)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## [4.0.12](https://github.com/availity/sdk-js/compare/@availity/native-form@4.0.10...@availity/native-form@4.0.12) (2022-02-22)

**Note:** Version bump only for package @availity/native-form





## [4.0.11](https://github.com/availity/sdk-js/compare/@availity/native-form@4.0.10...@availity/native-form@4.0.11) (2022-01-19)

**Note:** Version bump only for package @availity/native-form





## 4.0.10 (2021-12-21)

**Note:** Version bump only for package @availity/native-form





## [4.0.9](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.8...@availity/native-form@4.0.9) (2021-10-29)

**Note:** Version bump only for package @availity/native-form





## [4.0.8](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.7...@availity/native-form@4.0.8) (2021-10-22)

**Note:** Version bump only for package @availity/native-form





## 4.0.7 (2021-10-20)

**Note:** Version bump only for package @availity/native-form





## [4.0.6](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.5...@availity/native-form@4.0.6) (2021-10-19)

**Note:** Version bump only for package @availity/native-form





## [4.0.5](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.4...@availity/native-form@4.0.5) (2021-10-08)


### Bug Fixes

* **native-form:** update types and use api-axiosv6 ([02b0cd6](https://github.com/Availity/sdk-js/commit/02b0cd6a6c1f401c71b4ddc8482297604531983a))





## [4.0.4](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.3...@availity/native-form@4.0.4) (2021-09-30)

**Note:** Version bump only for package @availity/native-form





## [4.0.3](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.2...@availity/native-form@4.0.3) (2021-09-30)

**Note:** Version bump only for package @availity/native-form





## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.1...@availity/native-form@4.0.2) (2021-09-28)

**Note:** Version bump only for package @availity/native-form





## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/native-form@4.0.0...@availity/native-form@4.0.1) (2021-09-16)

**Note:** Version bump only for package @availity/native-form





# 4.0.0 (2021-09-01)


### Features

* **native-form:** add operationName to graphql request for query tracking ([5772f57](https://github.com/Availity/sdk-js/commit/5772f57f30278ede6d85daefe2ab7341e8b78b92))
* **native-form:** call avWebQLApi to obtain sso type ([23a1bd9](https://github.com/Availity/sdk-js/commit/23a1bd98cc71334454d137be822a5a5a23a32447))


### BREAKING CHANGES

* **native-form:** native-form now exports an async function, no longer defaults type
MIGRATION:
 Before: nativeForm(...args);
 After: await nativeForm(...args);





# 3.0.0 (2021-05-17)


### Code Refactoring

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** eslint fixes ([00e3395](https://github.com/Availity/sdk-js/commit/00e339595962501c96acf2895650f104d4c49809))


### BREAKING CHANGES

* **analytics-core,api-core,authorizations-core,message-core,native-form,upload-core:** .filter()[0] replaced with .find(), .forEach() replaced with for...of





## 2.8.22 (2020-06-22)

**Note:** Version bump only for package @availity/native-form





## 2.8.21 (2020-05-01)

**Note:** Version bump only for package @availity/native-form





## 2.8.20 (2020-04-30)

**Note:** Version bump only for package @availity/native-form





## 2.8.19 (2020-04-22)

**Note:** Version bump only for package @availity/native-form





## 2.8.18 (2020-04-08)

**Note:** Version bump only for package @availity/native-form





## 2.8.17 (2020-04-06)

**Note:** Version bump only for package @availity/native-form





## 2.8.16 (2020-04-06)

**Note:** Version bump only for package @availity/native-form





## [2.8.15](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.12...@availity/native-form@2.8.15) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.14](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.13...@availity/native-form@2.8.14) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.13](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.11...@availity/native-form@2.8.13) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.12](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.10...@availity/native-form@2.8.12) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [2.8.11](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.10...@availity/native-form@2.8.11) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [2.8.10](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.7...@availity/native-form@2.8.10) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.9](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.8...@availity/native-form@2.8.9) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.8](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.7...@availity/native-form@2.8.8) (2020-02-13)

**Note:** Version bump only for package @availity/native-form





## [2.8.7](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.6...@availity/native-form@2.8.7) (2020-01-14)

### Bug Fixes

-   **native-form:** fixed error trying to call toString on undefined ([c009460](https://github.com/Availity/sdk-js/commit/c009460))
-   **native-form:** updated handling of falsey values ([ab57615](https://github.com/Availity/sdk-js/commit/ab57615))
-   **native-form:** updated test suite name ([fbf824c](https://github.com/Availity/sdk-js/commit/fbf824c))

## [2.8.6](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.5...@availity/native-form@2.8.6) (2020-01-03)

**Note:** Version bump only for package @availity/native-form

## [2.8.5](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.4...@availity/native-form@2.8.5) (2020-01-03)

**Note:** Version bump only for package @availity/native-form

## [2.8.4](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.3...@availity/native-form@2.8.4) (2019-10-29)

**Note:** Version bump only for package @availity/native-form

## [2.8.3](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.2...@availity/native-form@2.8.3) (2019-09-27)

**Note:** Version bump only for package @availity/native-form

## [2.8.2](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.1...@availity/native-form@2.8.2) (2019-02-20)

### Bug Fixes

-   append function is not defined for ie and edge mobile ([d995343](https://github.com/Availity/sdk-js/commit/d995343))

## [2.8.1](https://github.com/Availity/sdk-js/compare/@availity/native-form@2.8.0...@availity/native-form@2.8.1) (2019-02-12)

**Note:** Version bump only for package @availity/native-form

# 2.8.0 (2019-02-12)

# 2.5.0 (2018-08-20)

### Features

-   **native-form:** add native form ([#62](https://github.com/Availity/sdk-js/issues/62)) ([8aa0471](https://github.com/Availity/sdk-js/commit/8aa0471))

<a name="2.7.0"></a>

# 2.7.0 (2018-11-20)

<a name="2.5.0"></a>

# 2.5.0 (2018-08-20)

### Features

-   **native-form:** add native form ([#62](https://github.com/Availity/sdk-js/issues/62)) ([8aa0471](https://github.com/Availity/sdk-js/commit/8aa0471))
