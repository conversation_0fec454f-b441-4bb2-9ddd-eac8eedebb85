# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [5.0.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@4.0.2...@availity/env-var@5.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [4.0.2](https://github.com/Availity/sdk-js/compare/@availity/env-var@4.0.1...@availity/env-var@4.0.2) (2025-03-14)



## [4.0.1](https://github.com/Availity/sdk-js/compare/@availity/env-var@4.0.0...@availity/env-var@4.0.1) (2025-03-10)



# [4.0.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@3.1.1...@availity/env-var@4.0.0) (2024-07-29)


### chore

* **env-var:** upgrade to node 18 and 20 ([28cb33d](https://github.com/Availity/sdk-js/commit/28cb33da8f0c15922472bbfcf6a1629ee332cfb9))


### BREAKING CHANGES

* **env-var:** drop support for node 14 and 16



## [3.1.1](https://github.com/Availity/sdk-js/compare/@availity/env-var@3.1.0...@availity/env-var@3.1.1) (2024-07-29)



# [3.1.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@3.0.2...@availity/env-var@3.1.0) (2024-06-27)


### Features

* essentials envVar ([d4206ca](https://github.com/Availity/sdk-js/commit/d4206ca0771007c7ef13f1debd9b7d4a7a2ced69))



## [3.0.2](https://github.com/Availity/sdk-js/compare/@availity/env-var@3.0.1...@availity/env-var@3.0.2) (2024-05-30)



## [3.0.1](https://github.com/Availity/sdk-js/compare/@availity/env-var@3.0.0...@availity/env-var@3.0.1) (2024-02-19)



# [3.0.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@2.0.4...@availity/env-var@3.0.0) (2023-09-01)


### Features

* **env-var:** allow users to return undefined ([947687a](https://github.com/Availity/sdk-js/commit/947687a5f1430569ca8b698435d5632d19431c05))


### BREAKING CHANGES

* **env-var:** returning undefined previously would default to the local value,
now we expect you to define what you want to return



## [2.0.4](https://github.com/Availity/sdk-js/compare/@availity/env-var@2.0.3...@availity/env-var@2.0.4) (2023-08-23)



# 1.0.0 (2023-08-23)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))
* **env-var:** default value not environment ([076540b](https://github.com/Availity/sdk-js/commit/076540b212d419291f0ec76be1e2ba636c68e70b))
* update babel and jest configs, rename tests ([c80e194](https://github.com/Availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))


* feat(env-var)!: update types ([d747314](https://github.com/Availity/sdk-js/commit/d747314b481c41a1c5da11613df9d0ad9ae6988d))
* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### Features

* **env-var:** add env support for cloud urls ([2534563](https://github.com/Availity/sdk-js/commit/2534563e7ced303d6095101733e5d64a22210ef6))
* **env-var:** add env-vars package ([a5a2409](https://github.com/Availity/sdk-js/commit/a5a2409ad6d8c19a23fe2b35501d37fe40838b88))
* **env-var:** add sandbox support to cloud environment ([8ada30c](https://github.com/Availity/sdk-js/commit/8ada30cabe64eb95cc37f3b3e0d68444585c0373))
* **env-var:** added extra param for setting default env var ([2f97472](https://github.com/Availity/sdk-js/commit/2f97472efb091358ba6728bb7c4169882dd3a402))


### BREAKING CHANGES

* Drop IE support
* Drop Internet Explorer support



## [2.0.3](https://github.com/Availity/sdk-js/compare/@availity/env-var@2.0.2...@availity/env-var@2.0.3) (2022-06-15)



## [2.0.2](https://github.com/Availity/sdk-js/compare/@availity/env-var@2.0.1...@availity/env-var@2.0.2) (2022-05-24)



## [2.0.1](https://github.com/Availity/sdk-js/compare/@availity/env-var@2.0.0...@availity/env-var@2.0.1) (2022-04-28)


### Bug Fixes

* add browser field for output ([0ce7170](https://github.com/Availity/sdk-js/commit/0ce717075a82675b8707e4db0cc07cd4af370f3d))



# [2.0.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@1.11.3...@availity/env-var@2.0.0) (2022-04-28)


* feat(env-var)!: update types ([d747314](https://github.com/Availity/sdk-js/commit/d747314b481c41a1c5da11613df9d0ad9ae6988d))
* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop IE support
* Drop Internet Explorer support



## [1.11.4-alpha.0](https://github.com/Availity/sdk-js/compare/@availity/env-var@1.11.3...@availity/env-var@1.11.4-alpha.0) (2022-04-18)


* build!: switch from lerna to nx and compile to esm with tsup ([c586085](https://github.com/Availity/sdk-js/commit/c5860856ca96b743a0653d335ea00f0889132f7f))


### BREAKING CHANGES

* Drop Internet Explorer support



## 1.11.3 (2021-12-21)

**Note:** Version bump only for package @availity/env-var





## 1.11.2 (2021-10-22)

**Note:** Version bump only for package @availity/env-var





## 1.11.1 (2021-05-17)

**Note:** Version bump only for package @availity/env-var





# 1.11.0 (2020-06-03)


### Features

* **env-var:** add sandbox support to cloud environment ([8ada30c](https://github.com/availity/sdk-js/commit/8ada30cabe64eb95cc37f3b3e0d68444585c0373))





# 1.10.0 (2020-05-20)


### Features

* **env-var:** add env support for cloud urls ([2534563](https://github.com/availity/sdk-js/commit/2534563e7ced303d6095101733e5d64a22210ef6))





## 1.9.20 (2020-05-01)

**Note:** Version bump only for package @availity/env-var





## 1.9.19 (2020-04-30)

**Note:** Version bump only for package @availity/env-var





## 1.9.18 (2020-04-22)

**Note:** Version bump only for package @availity/env-var





## 1.9.17 (2020-04-08)

**Note:** Version bump only for package @availity/env-var





## 1.9.16 (2020-04-06)

**Note:** Version bump only for package @availity/env-var





## 1.9.15 (2020-04-06)

**Note:** Version bump only for package @availity/env-var





## [1.9.14](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.11...@availity/env-var@1.9.14) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.13](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.12...@availity/env-var@1.9.13) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.12](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.10...@availity/env-var@1.9.12) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.11](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.9...@availity/env-var@1.9.11) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.9.10](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.9...@availity/env-var@1.9.10) (2020-02-13)


### Bug Fixes

* update babel and jest configs, rename tests ([c80e194](https://github.com/availity/sdk-js/commit/c80e1947f0c3cb28c3c7db842c82f381622d72e7))





## [1.9.9](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.6...@availity/env-var@1.9.9) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.8](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.7...@availity/env-var@1.9.8) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.7](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.6...@availity/env-var@1.9.7) (2020-02-13)

**Note:** Version bump only for package @availity/env-var





## [1.9.6](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.5...@availity/env-var@1.9.6) (2020-02-09)

**Note:** Version bump only for package @availity/env-var





## [1.9.5](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.4...@availity/env-var@1.9.5) (2020-01-23)

**Note:** Version bump only for package @availity/env-var





## [1.9.4](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.3...@availity/env-var@1.9.4) (2020-01-03)

**Note:** Version bump only for package @availity/env-var

## [1.9.3](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.2...@availity/env-var@1.9.3) (2020-01-03)

**Note:** Version bump only for package @availity/env-var

## [1.9.2](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.1...@availity/env-var@1.9.2) (2019-10-29)

**Note:** Version bump only for package @availity/env-var

## [1.9.1](https://github.com/availity/sdk-js/compare/@availity/env-var@1.9.0...@availity/env-var@1.9.1) (2019-04-17)

**Note:** Version bump only for package @availity/env-var

# [1.9.0](https://github.com/availity/sdk-js/compare/@availity/env-var@1.7.1...@availity/env-var@1.9.0) (2019-03-18)

### Features

-   **env-var:** added extra param for setting default env var ([2f97472](https://github.com/availity/sdk-js/commit/2f97472))

# [1.8.0](https://github.com/availity/sdk-js/compare/@availity/env-var@1.7.1...@availity/env-var@1.8.0) (2019-03-04)

### Features

-   **env-var:** added extra param for setting default env var ([2f97472](https://github.com/availity/sdk-js/commit/2f97472))

## [1.7.1](https://github.com/availity/sdk-js/compare/@availity/env-var@1.7.0...@availity/env-var@1.7.1) (2019-02-12)

**Note:** Version bump only for package @availity/env-var

# 1.7.0 (2019-02-12)

## 1.6.2 (2018-04-24)

### Bug Fixes

-   **env-var:** default value not environment ([076540b](https://github.com/availity/sdk-js/commit/076540b))

# 1.6.0 (2018-04-20)

### Features

-   **env-var:** add env-vars package ([a5a2409](https://github.com/availity/sdk-js/commit/a5a2409))
