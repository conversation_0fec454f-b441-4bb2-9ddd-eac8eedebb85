{"name": "@availity/user-activity-broadcaster", "version": "1.0.0", "description": "This package broacasts user activity to the navigation.", "main": "src/index.js", "keywords": [], "author": "<PERSON> <<EMAIL>>", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "license": "MIT", "devDependencies": {"tsup": "^8.4.0"}, "publishConfig": {"access": "public"}}