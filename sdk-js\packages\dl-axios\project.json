{"name": "dl-axios", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/dl-axios"], "options": {"jestConfig": "packages/dl-axios/jest.config.js"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "angular", "commitMessageFormat": "chore({projectName}): release version ${version} [skip ci]", "tagPrefix": "@availity/{projectName}@", "baseBranch": "master", "trackDeps": true}}, "lint": {"executor": "@nx/eslint:lint", "options": {"eslintConfig": ".eslintrc.yaml", "silent": false, "fix": false, "cache": true, "cacheLocation": "./node_modules/.cache/dl-axios/.eslintcache", "maxWarnings": -1, "quiet": false, "noEslintrc": false, "hasTypeAwareRules": true, "cacheStrategy": "metadata"}}}}