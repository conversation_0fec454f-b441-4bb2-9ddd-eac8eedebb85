{"name": "@availity/exceptions-axios", "version": "6.0.2", "description": "Availity class to log exceptions via axios", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"@availity/api-axios": "workspace:*", "@availity/exceptions-core": "workspace:*"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}