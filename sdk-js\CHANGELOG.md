<a name="2.10.0"></a>

# [2.10.0](https://github.com/Availity/sdk-js/compare/v3.0.0...v2.10.0) (2018-11-20)

### Bug Fixes

-   **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6))
-   **api-core:** Allow array for permissions ([#67](https://github.com/Availity/sdk-js/issues/67)) ([98adf76](https://github.com/Availity/sdk-js/commit/98adf76))
-   **api-core:** filesDelivery merge ([eb75160](https://github.com/Availity/sdk-js/commit/eb75160))
-   **api-core:** typo fix ([f543296](https://github.com/Availity/sdk-js/commit/f543296))
-   **upload-core:** auto retry failed uploads ([#73](https://github.com/Availity/sdk-js/issues/73)) ([c602275](https://github.com/Availity/sdk-js/commit/c602275)), closes [#71](https://github.com/Availity/sdk-js/issues/71)

### Features

-   add users to axios/angular api ([6647be5](https://github.com/Availity/sdk-js/commit/6647be5))
-   **api-core:** add example response for file delivery ([edc4df5](https://github.com/Availity/sdk-js/commit/edc4df5))
-   **api-core:** add file upload delivery batch api ([eff0a3c](https://github.com/Availity/sdk-js/commit/eff0a3c))
-   **api-core:** helper function for settings api ([4d405a7](https://github.com/Availity/sdk-js/commit/4d405a7))

### BREAKING CHANGES

-   **api-core:** The query to the API has been changed. To get the previous behavior, supply a custom paramSerializer.

<a name="2.9.0"></a>

# [2.9.0](https://github.com/Availity/sdk-js/compare/v3.0.0...v2.9.0) (2018-11-20)

### Bug Fixes

-   **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6))
-   **api-core:** Allow array for permissions ([#67](https://github.com/Availity/sdk-js/issues/67)) ([98adf76](https://github.com/Availity/sdk-js/commit/98adf76))
-   **api-core:** filesDelivery merge ([eb75160](https://github.com/Availity/sdk-js/commit/eb75160))
-   **api-core:** typo fix ([f543296](https://github.com/Availity/sdk-js/commit/f543296))
-   **upload-core:** auto retry failed uploads ([#73](https://github.com/Availity/sdk-js/issues/73)) ([c602275](https://github.com/Availity/sdk-js/commit/c602275)), closes [#71](https://github.com/Availity/sdk-js/issues/71)

### Features

-   add users to axios/angular api ([6647be5](https://github.com/Availity/sdk-js/commit/6647be5))
-   **api-core:** add example response for file delivery ([edc4df5](https://github.com/Availity/sdk-js/commit/edc4df5))
-   **api-core:** add file upload delivery batch api ([eff0a3c](https://github.com/Availity/sdk-js/commit/eff0a3c))
-   **api-core:** helper function for settings api ([4d405a7](https://github.com/Availity/sdk-js/commit/4d405a7))

### BREAKING CHANGES

-   **api-core:** The query to the API has been changed. To get the previous behavior, supply a custom paramSerializer.

<a name="2.8.0"></a>

# [2.8.0](https://github.com/Availity/sdk-js/compare/v3.0.0...v2.8.0) (2018-11-20)

### Bug Fixes

-   **api-axios:** use merge-options-es5 instead of merge-options ([993ccc6](https://github.com/Availity/sdk-js/commit/993ccc6))
-   **api-core:** Allow array for permissions ([#67](https://github.com/Availity/sdk-js/issues/67)) ([98adf76](https://github.com/Availity/sdk-js/commit/98adf76))
-   **api-core:** filesDelivery merge ([eb75160](https://github.com/Availity/sdk-js/commit/eb75160))
-   **api-core:** typo fix ([f543296](https://github.com/Availity/sdk-js/commit/f543296))
-   **upload-core:** auto retry failed uploads ([#73](https://github.com/Availity/sdk-js/issues/73)) ([c602275](https://github.com/Availity/sdk-js/commit/c602275)), closes [#71](https://github.com/Availity/sdk-js/issues/71)

### Features

-   add users to axios/angular api ([6647be5](https://github.com/Availity/sdk-js/commit/6647be5))
-   **api-core:** add example response for file delivery ([edc4df5](https://github.com/Availity/sdk-js/commit/edc4df5))
-   **api-core:** add file upload delivery batch api ([eff0a3c](https://github.com/Availity/sdk-js/commit/eff0a3c))
-   **api-core:** helper function for settings api ([4d405a7](https://github.com/Availity/sdk-js/commit/4d405a7))

### BREAKING CHANGES

-   **api-core:** The query to the API has been changed. To get the previous behavior, supply a custom paramSerializer.

<a name="2.7.0"></a>

# [2.7.0](https://github.com/Availity/sdk-js/compare/v2.6.0...v2.7.0) (2018-09-06)

### Bug Fixes

-   **api-axios:** us merge-options for merge ([25b75b4](https://github.com/Availity/sdk-js/commit/25b75b4))

### Code Refactoring

-   **message-core:** AvMessage ([4d2d0be](https://github.com/Availity/sdk-js/commit/4d2d0be))

### Features

-   **message-core:** add unsubscribe ([dd6b5eb](https://github.com/Availity/sdk-js/commit/dd6b5eb))
-   **message-core:** subscriber functionality ([343b09d](https://github.com/Availity/sdk-js/commit/343b09d))
-   **message-core:** unsusbscribeAll fn ([3a66ff8](https://github.com/Availity/sdk-js/commit/3a66ff8))

### BREAKING CHANGES

-   **message-core:** Remove message-angular to use subscribers from core

<a name="2.6.0"></a>

# [2.6.0](https://github.com/Availity/sdk-js/compare/v2.5.0...v2.6.0) (2018-08-23)

### Features

-   **api-core:** helper function for settings api ([#64](https://github.com/Availity/sdk-js/issues/64)) ([f247235](https://github.com/Availity/sdk-js/commit/f247235))

<a name="2.5.0"></a>

# [2.5.0](https://github.com/Availity/sdk-js/compare/v2.4.8...v2.5.0) (2018-08-20)

### Features

-   **native-form:** add native form ([#62](https://github.com/Availity/sdk-js/issues/62)) ([8aa0471](https://github.com/Availity/sdk-js/commit/8aa0471))

<a name="2.4.8"></a>

## [2.4.8](https://github.com/Availity/sdk-js/compare/v2.4.7...v2.4.8) (2018-08-06)

### Bug Fixes

-   **api-axios:** replace axios merge with deep-assign ([a7183b1](https://github.com/Availity/sdk-js/commit/a7183b1))
-   **api-core:** spread args for aliases ([5c7516b](https://github.com/Availity/sdk-js/commit/5c7516b))

<a name="2.4.7"></a>

## [2.4.7](https://github.com/Availity/sdk-js/compare/v2.4.6...v2.4.7) (2018-08-02)

### Bug Fixes

-   **api-core:** check for lowercase location as well ([4a4a9a0](https://github.com/Availity/sdk-js/commit/4a4a9a0))

<a name="2.4.6"></a>

## [2.4.6](https://github.com/Availity/sdk-js/compare/v2.4.4...v2.4.6) (2018-08-02)

### Bug Fixes

-   **analytics-core:** fix non-click events ([fce9b26](https://github.com/Availity/sdk-js/commit/fce9b26))

<a name="2.4.5"></a>

## [2.4.5](https://github.com/Availity/sdk-js/compare/v2.4.4...v2.4.5) (2018-08-02)

### Bug Fixes

-   **analytics-core:** fix non-click events ([fce9b26](https://github.com/Availity/sdk-js/commit/fce9b26))

<a name="2.4.4"></a>

## [2.4.4](https://github.com/Availity/sdk-js/compare/v2.4.3...v2.4.4) (2018-07-25)

### Bug Fixes

-   **analytics-core:** remove camelcase dep ([fc1be33](https://github.com/Availity/sdk-js/commit/fc1be33))

<a name="2.4.3"></a>

## [2.4.3](https://github.com/Availity/sdk-js/compare/v2.4.2...v2.4.3) (2018-07-13)

### Bug Fixes

-   **dl-axios:** fix export class ([9d470e0](https://github.com/Availity/sdk-js/commit/9d470e0))

<a name="2.4.2"></a>

## [2.4.2](https://github.com/Availity/sdk-js/compare/v2.4.1...v2.4.2) (2018-07-13)

### Bug Fixes

-   **dl-axios:** fix import ([d42db46](https://github.com/Availity/sdk-js/commit/d42db46))

<a name="2.4.1"></a>

## [2.4.1](https://github.com/Availity/sdk-js/compare/v2.4.0...v2.4.1) (2018-07-13)

### Bug Fixes

-   **dl-core:** fix import ([a25924d](https://github.com/Availity/sdk-js/commit/a25924d))

<a name="2.4.0"></a>

# [2.4.0](https://github.com/Availity/sdk-js/compare/v2.3.0...v2.4.0) (2018-07-12)

### Bug Fixes

-   **upload-core:** IE compatibility no array spreads ([7444173](https://github.com/Availity/sdk-js/commit/7444173))

### Features

-   **analytics-core:** add autotrack ([849909e](https://github.com/Availity/sdk-js/commit/849909e))
-   **download-angular:** add helpers to download files from microservices ([4809f6f](https://github.com/Availity/sdk-js/commit/4809f6f))
-   **download-core:** fix imports and add download-axios ([20c0f51](https://github.com/Availity/sdk-js/commit/20c0f51))

<a name="2.3.3"></a>

## [2.3.3](https://github.com/Availity/sdk-js/compare/v2.3.0...v2.3.3) (2018-07-12)

### Features

-   **download-angular:** add helpers to download files from microservices ([4809f6f](https://github.com/Availity/sdk-js/commit/4809f6f))
-   **download-core:** fix imports and add download-axios ([20c0f51](https://github.com/Availity/sdk-js/commit/20c0f51))

<a name="2.3.0"></a>

# [2.3.0](https://github.com/Availity/sdk-js/compare/v2.2.1...v2.3.0) (2018-06-29)

### Features

-   **expections-axios:** add basic axios wrapper for execptions ([61803a9](https://github.com/Availity/sdk-js/commit/61803a9))

<a name="2.2.1"></a>

## [2.2.1](https://github.com/Availity/sdk-js/compare/v2.2.0...v2.2.1) (2018-06-29)

### Bug Fixes

-   allow exceptions-core to be published ([0c15350](https://github.com/Availity/sdk-js/commit/0c15350))

<a name="2.2.0"></a>

# [2.2.0](https://github.com/Availity/sdk-js/compare/v2.1.2...v2.2.0) (2018-06-29)

### Bug Fixes

-   **api-core:** format postGet payload to standard ([5b885cf](https://github.com/Availity/sdk-js/commit/5b885cf))

### Features

-   **exception-core:** use stacktrace-js for better stacktraces ([e59fd00](https://github.com/Availity/sdk-js/commit/e59fd00))

<a name="2.1.2"></a>

## [2.1.2](https://github.com/Availity/sdk-js/compare/v2.1.1...v2.1.2) (2018-06-13)

### Bug Fixes

-   **api-core:** attempts count not increasing ([2e414a3](https://github.com/Availity/sdk-js/commit/2e414a3))
-   **upload-core:** upload filename check ([cb98b56](https://github.com/Availity/sdk-js/commit/cb98b56))

<a name="2.1.1"></a>

## [2.1.1](https://github.com/Availity/sdk-js/compare/v2.1.0...v2.1.1) (2018-05-25)

### Bug Fixes

-   **api-core:** fix usage of Promise ([8fb3de8](https://github.com/Availity/sdk-js/commit/8fb3de8))

<a name="2.1.0"></a>

# [2.1.0](https://github.com/Availity/sdk-js/compare/v2.0.5...v2.1.0) (2018-05-24)

### Features

-   **api-core:** add all method to get all the data from all of the pages ([114519d](https://github.com/Availity/sdk-js/commit/114519d))

<a name="2.0.5"></a>

## [2.0.5](https://github.com/Availity/sdk-js/compare/v2.0.4...v2.0.5) (2018-05-18)

### Bug Fixes

-   **upload-core:** package.json main file path fix ([0f5d91c](https://github.com/Availity/sdk-js/commit/0f5d91c))

<a name="2.0.4"></a>

## [2.0.4](https://github.com/Availity/sdk-js/compare/v2.0.3...v2.0.4) (2018-05-17)

### Bug Fixes

-   **upload-core:** change message when file is decrypting ([416a654](https://github.com/Availity/sdk-js/commit/416a654))

<a name="2.0.3"></a>

## [2.0.3](https://github.com/Availity/sdk-js/compare/v2.0.2...v2.0.3) (2018-05-10)

### Bug Fixes

-   **api-core:** allow id to be in the config ([55b55af](https://github.com/Availity/sdk-js/commit/55b55af))
-   **upload-core:** fix file path issue ([8c5d974](https://github.com/Availity/sdk-js/commit/8c5d974))

<a name="2.0.2"></a>

## [2.0.2](https://github.com/Availity/sdk-js/compare/v2.0.1...v2.0.2) (2018-05-04)

### Bug Fixes

-   api implementation peerDeps ([28c163d](https://github.com/Availity/sdk-js/commit/28c163d))

<a name="2.0.1"></a>

## [2.0.1](https://github.com/Availity/sdk-js/compare/v2.0.0...v2.0.1) (2018-05-04)

### Bug Fixes

-   **api-core:** get id out of arguments ([3e525c2](https://github.com/Availity/sdk-js/commit/3e525c2))

<a name="2.0.0"></a>

# [2.0.0](https://github.com/Availity/sdk-js/compare/v1.6.3...v2.0.0) (2018-05-04)

### Bug Fixes

-   **api-axios:** fix ms to extend core ms ([a2f65e5](https://github.com/Availity/sdk-js/commit/a2f65e5))
-   **api-core:** avSpaces - throw error when no spaceId given ([7c47280](https://github.com/Availity/sdk-js/commit/7c47280))

### Chores

-   **api-core:** core, angular, axios api naming consistency ([f129fad](https://github.com/Availity/sdk-js/commit/f129fad))

### BREAKING CHANGES

-   **api-core:** export naming conventions have been refactored to match for each implementation (angular/axios). Constructors are prefixed with 'Av', implementations are prefixed with 'av', and Apis are postfixed with 'Api'.

<a name="1.6.3"></a>

## [1.6.3](https://github.com/Availity/sdk-js/compare/v1.6.2...v1.6.3) (2018-04-24)

### Bug Fixes

-   **upload-core:** check xhr error message from header ([1609d58](https://github.com/Availity/sdk-js/commit/1609d58))
-   **upload-core:** update test ([65efc5a](https://github.com/Availity/sdk-js/commit/65efc5a))

<a name="1.6.2"></a>

## [1.6.2](https://github.com/Availity/sdk-js/compare/v1.6.1...v1.6.2) (2018-04-24)

### Bug Fixes

-   **env-var:** default value not environment ([076540b](https://github.com/Availity/sdk-js/commit/076540b))

<a name="1.6.1"></a>

## [1.6.1](https://github.com/Availity/sdk-js/compare/v1.6.0...v1.6.1) (2018-04-23)

### Bug Fixes

-   **upload-core:** pass xhr error message to UI ([6676650](https://github.com/Availity/sdk-js/commit/6676650))

<a name="1.6.0"></a>

# [1.6.0](https://github.com/Availity/sdk-js/compare/v1.5.1...v1.6.0) (2018-04-20)

### Features

-   **env-var:** add env-vars package ([a5a2409](https://github.com/Availity/sdk-js/commit/a5a2409))

<a name="1.5.1"></a>

## [1.5.1](https://github.com/Availity/sdk-js/compare/v1.5.0...v1.5.1) (2018-04-18)

### Bug Fixes

-   **upload-core:** fix for pending decryption status ([d05df81](https://github.com/Availity/sdk-js/commit/d05df81))

<a name="1.5.0"></a>

# [1.5.0](https://github.com/Availity/sdk-js/compare/v1.4.1...v1.5.0) (2018-04-18)

### Bug Fixes

-   **upload-core:** fix encryption conditions ([3d1b517](https://github.com/Availity/sdk-js/commit/3d1b517))

### Features

-   **upload-core:** adding encryption ([e08b1b2](https://github.com/Availity/sdk-js/commit/e08b1b2))

<a name="1.4.1"></a>

## [1.4.1](https://github.com/Availity/sdk-js/compare/v1.4.0...v1.4.1) (2018-04-18)

### Bug Fixes

-   **upload:** replace object.values usage ([8967629](https://github.com/Availity/sdk-js/commit/8967629))

<a name="1.4.0"></a>

# [1.4.0](https://github.com/Availity/sdk-js/compare/v1.3.0...v1.4.0) (2018-04-17)

### Features

-   **upload-core:** modify file figerprint to use metadata ([d45ac0c](https://github.com/Availity/sdk-js/commit/d45ac0c))

<a name="1.3.0"></a>

# [1.3.0](https://github.com/Availity/sdk-js/compare/v1.2.0...v1.3.0) (2018-04-13)

### Bug Fixes

-   **api-angular:** fix MS extends ([24544cc](https://github.com/Availity/sdk-js/commit/24544cc))

<a name="1.2.0"></a>

# [1.2.0](https://github.com/Availity/sdk-js/compare/v1.1.4...v1.2.0) (2018-04-12)

### Bug Fixes

-   **api-angular:** pass merge function to ms ([6a3ea3c](https://github.com/Availity/sdk-js/commit/6a3ea3c))

### Features

-   **api-core:** refactor polling for extending MS ([04c1474](https://github.com/Availity/sdk-js/commit/04c1474))

<a name="1.1.4"></a>

## [1.1.4](https://github.com/Availity/sdk-js/compare/v1.1.3...v1.1.4) (2018-04-10)

### Bug Fixes

-   **upload-core:** clean up attributes ([4b320c9](https://github.com/Availity/sdk-js/commit/4b320c9))
-   **upload-core:** clean up attributes ([5860c4f](https://github.com/Availity/sdk-js/commit/5860c4f))
-   **upload-core:** remove redundant if check ([6d7a973](https://github.com/Availity/sdk-js/commit/6d7a973))

### Features

-   **upload-core:** add extra metadata field ([577c001](https://github.com/Availity/sdk-js/commit/577c001))

<a name="1.1.3"></a>

## [1.1.3](https://github.com/Availity/sdk-js/compare/v1.1.2...v1.1.3) (2018-04-04)

### Bug Fixes

-   **upload-core:** add test cases ([86409f8](https://github.com/Availity/sdk-js/commit/86409f8))
-   **upload-core:** fix lint ([0b2054e](https://github.com/Availity/sdk-js/commit/0b2054e))
-   **upload-core:** return file check error at the same time ([a5f88e8](https://github.com/Availity/sdk-js/commit/a5f88e8))
-   **upload-core:** send tus metadata mapping ([6041339](https://github.com/Availity/sdk-js/commit/6041339))
-   **upload-core:** tus attachmentName value ([fb3de99](https://github.com/Availity/sdk-js/commit/fb3de99))

### Features

-   **upload-core:** add file metadata via tus ([f8a4cf1](https://github.com/Availity/sdk-js/commit/f8a4cf1)), closes [#20](https://github.com/Availity/sdk-js/issues/20)

<a name="1.1.2"></a>

## [1.1.2](https://github.com/Availity/sdk-js/compare/v1.1.1...v1.1.2) (2018-03-28)

<a name="1.1.1"></a>

## [1.1.1](https://github.com/Availity/sdk-js/compare/v1.0.2...v1.1.1) (2018-03-28)

### Features

-   **keyv-local-sync:** init commit ([9a3d400](https://github.com/Availity/sdk-js/commit/9a3d400))

<a name="1.0.2"></a>

## [1.0.2](https://github.com/Availity/sdk-js/compare/v1.0.1...v1.0.2) (2018-03-27)

### Bug Fixes

-   **upload-core:** add max file size ([447dd00](https://github.com/Availity/sdk-js/commit/447dd00))

<a name="1.0.1"></a>

## [1.0.1](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.17...v1.0.1) (2018-03-26)

### Bug Fixes

-   **api-core:** avFiles error handling ([3dd5392](https://github.com/Availity/sdk-js/commit/3dd5392))
-   **upload-core:** error handling ([14fa7ba](https://github.com/Availity/sdk-js/commit/14fa7ba))
-   **upload-core:** fix test case ([831e1b4](https://github.com/Availity/sdk-js/commit/831e1b4))
-   **upload-core:** move file type check to start ([b3dd3dd](https://github.com/Availity/sdk-js/commit/b3dd3dd))

### Features

-   **upload-core:** upload core file types check ([#7](https://github.com/Availity/sdk-js/issues/7)) ([080d887](https://github.com/Availity/sdk-js/commit/080d887))
-   **uploads:** capture error message ([#6](https://github.com/Availity/sdk-js/issues/6)) ([5af448e](https://github.com/Availity/sdk-js/commit/5af448e))

<a name="1.0.0"></a>

# [1.0.0](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.17...v1.0.0) (2018-03-05)

### Features

-   **upload-core:** upload core file types check ([#7](https://github.com/Availity/sdk-js/issues/7)) ([080d887](https://github.com/Availity/sdk-js/commit/080d887))
-   **uploads:** capture error message ([#6](https://github.com/Availity/sdk-js/issues/6)) ([5af448e](https://github.com/Availity/sdk-js/commit/5af448e))

<a name="1.0.0-alpha.17"></a>

# [1.0.0-alpha.17](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.16...v1.0.0-alpha.17) (2018-01-18)

### Bug Fixes

-   **upload-core:** serveral fixes ([1e0d0e4](https://github.com/Availity/sdk-js/commit/1e0d0e4))

<a name="1.0.0-alpha.16"></a>

# [1.0.0-alpha.16](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.15...v1.0.0-alpha.16) (2018-01-17)

### Features

-   **upload-core:** add progress indicator for file scans ([a84cc85](https://github.com/Availity/sdk-js/commit/a84cc85))

<a name="1.0.0-alpha.15"></a>

# [1.0.0-alpha.15](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.14...v1.0.0-alpha.15) (2018-01-12)

### Bug Fixes

-   **upload-core:** add additional headers ([9a10f16](https://github.com/Availity/sdk-js/commit/9a10f16))

<a name="1.0.0-alpha.14"></a>

# [1.0.0-alpha.14](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.13...v1.0.0-alpha.14) (2018-01-11)

<a name="1.0.0-alpha.13"></a>

# [1.0.0-alpha.13](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.12...v1.0.0-alpha.13) (2018-01-10)

### Features

-   **api-core:** add helper function adds name attribute to providers collection ([3b24341](https://github.com/Availity/sdk-js/commit/3b24341))

<a name="1.0.0-alpha.12"></a>

# [1.0.0-alpha.12](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.11...v1.0.0-alpha.12) (2018-01-09)

<a name="1.0.0-alpha.11"></a>

# [1.0.0-alpha.11](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.10...v1.0.0-alpha.11) (2018-01-06)

### Bug Fixes

-   **api-core:** resources incorrectly handling config ([9a5de1c](https://github.com/Availity/sdk-js/commit/9a5de1c))

### Code Refactoring

-   **api-core:** pass config object to core classes ([d7b859c](https://github.com/Availity/sdk-js/commit/d7b859c))

### BREAKING CHANGES

-   **api-core:** Options to core classes are being passed in as config object instead of parameters.

<a name="1.0.0-alpha.10"></a>

# [1.0.0-alpha.10](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.9...v1.0.0-alpha.10) (2018-01-04)

### Bug Fixes

-   **api-core:** remove/delete should not assume data payloads ([e45a9f6](https://github.com/Availity/sdk-js/commit/e45a9f6))

### BREAKING CHANGES

-   **api-core:** previously remove/delete would assume that data was being passed in the body of the request if the first param of the message signature was NOT a string or number. Now, the method assumes a config object is passed in instead of data. This allows the developers to pass in params or data as they see fit.

<a name="1.0.0-alpha.9"></a>

# [1.0.0-alpha.9](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.7...v1.0.0-alpha.9) (2018-01-03)

### Bug Fixes

-   **api-core:** provides api param not properly merged with defaults ([5f53e3d](https://github.com/Availity/sdk-js/commit/5f53e3d))

<a name="1.0.0-alpha.8"></a>

# [1.0.0-alpha.8](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.7...v1.0.0-alpha.8) (2018-01-03)

<a name="1.0.0-alpha.7"></a>

# [1.0.0-alpha.7](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.6...v1.0.0-alpha.7) (2018-01-03)

### Bug Fixes

-   **api-core:** use get instead of query for space name ([84dd26a](https://github.com/Availity/sdk-js/commit/84dd26a))

<a name="1.0.0-alpha.6"></a>

# [1.0.0-alpha.6](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.5...v1.0.0-alpha.6) (2017-12-20)

### Bug Fixes

-   **api-core:** fix method call for notifcations ([9604973](https://github.com/Availity/sdk-js/commit/9604973)

<a name="1.0.0-alpha.5"></a>

# [1.0.0-alpha.5](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.4...v1.0.0-alpha.5) (2017-12-20)

### Bug Fixes

-   **api-core:** wrong url for notifcations api ([acafc97](https://github.com/Availity/sdk-js/commit/acafc97))

<a name="1.0.0-alpha.4"></a>

# [1.0.0-alpha.4](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.3...v1.0.0-alpha.4) (2017-12-20)

### Bug Fixes

-   **api-angular:** fix service name for avNotificationsApi ([c79ee42](https://github.com/Availity/sdk-js/commit/c79ee42))
-   **api-core:** wrong url for pdf api ([9f4af1c](https://github.com/Availity/sdk-js/commit/9f4af1c))

<a name="1.0.0-alpha.3"></a>

# [1.0.0-alpha.3](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.2...v1.0.0-alpha.3) (2017-12-19)

### Bug Fixes

-   **api-core:** user.me() should return user object ([715c616](https://github.com/Availity/sdk-js/commit/715c616))

<a name="1.0.0-alpha.2"></a>

# [1.0.0-alpha.2](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.1...v1.0.0-alpha.2) (2017-12-19)

<a name="1.0.0-alpha.1"></a>

# [1.0.0-alpha.1](https://github.com/Availity/sdk-js/compare/v1.0.0-alpha.0...v1.0.0-alpha.1) (2017-12-19)

### Bug Fixes

-   **api-core:** remove default after\* response transformations ([6f17d3a](https://github.com/Availity/sdk-js/commit/6f17d3a))

### BREAKING CHANGES

-   **api-core:** The core API classes no longer apply after\* transformations on the response. Previously, only the collection data was being returned when making API calls which made it difficult to react to the metadata around the response (e.g. pagination). Developers will have to unwrap the response manually.

Example response callback:

```
onResponse(response) {
  response && response.data && response.data.user|| {};
}
```
