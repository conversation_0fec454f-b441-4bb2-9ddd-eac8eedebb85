{"name": "@availity/dockyard", "version": "3.0.0", "description": "Convert yup schema to a friendly docs object", "keywords": ["yup", "schema", "docs"], "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/dockyard"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.5.4", "yup": "^0.32.11"}, "publishConfig": {"access": "public"}}