# @availity/relay-id

> Small package containing helpers for encoding/decoding ids according to the relay specification

[![Version](https://img.shields.io/npm/v/@availity/relay-id.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/relay-id)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/relay-id.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/relay-id)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/relay-id?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/relay-id/package.json)

## Install

### NPM

```bash
npm install @availity/relay-id
```

### Yarn

```bash
yarn add @availity/relay-id
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/relay-id)
