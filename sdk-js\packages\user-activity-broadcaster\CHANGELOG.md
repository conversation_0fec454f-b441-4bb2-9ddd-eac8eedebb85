# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [1.0.0](https://github.com/Availity/sdk-js/compare/@availity/user-activity-broadcaster@0.2.2...@availity/user-activity-broadcaster@1.0.0) (2025-05-14)


### chore

* drop support for node 18 and add support for node 22 ([1e3dcc3](https://github.com/Availity/sdk-js/commit/1e3dcc3311021edc5691b1383aa393ebebe1d9db))


### BREAKING CHANGES

* drop support for node 18



## [0.2.2](https://github.com/Availity/sdk-js/compare/@availity/user-activity-broadcaster@0.2.1...@availity/user-activity-broadcaster@0.2.2) (2025-03-14)



## [0.2.1](https://github.com/Availity/sdk-js/compare/@availity/user-activity-broadcaster@0.2.0...@availity/user-activity-broadcaster@0.2.1) (2025-03-10)



# [0.2.0](https://github.com/Availity/sdk-js/compare/@availity/user-activity-broadcaster@0.1.0...@availity/user-activity-broadcaster@0.2.0) (2024-10-23)


### Features

* **user-activity-broadcaster:** fixing default package main path EB-719 ([eaa6952](https://github.com/Availity/sdk-js/commit/eaa6952e889129c2f3e633359791dd3c902baf4f))



# 0.1.0 (2024-10-23)


### Features

* **user-activity-broadcaster:** adding changelog file EB-719 ([1607004](https://github.com/Availity/sdk-js/commit/16070049522d8db8401d71f7d5d074dc0bc213c4))
* **user-activity-broadcaster:** adding ignore because we're not using typescript EB-719 ([b66e9b8](https://github.com/Availity/sdk-js/commit/b66e9b87c2dcd305cd1ccbba5980e7550cc1bd70))
* **user-activity-broadcaster:** adding package EB-719 ([1213b58](https://github.com/Availity/sdk-js/commit/1213b58c37fcec8f5f6298e6e66103150a9db61e))
* **user-activity-broadcaster:** fixing commit message EB-719 ([be9d03e](https://github.com/Availity/sdk-js/commit/be9d03ebcd1efd352eed7c1da46a3addf53b8fc8))



# 0.1.0-alpha.0 (2024-10-22)


### Features

* **user-activity-broadcaster:** adding changelog file EB-719 ([1607004](https://github.com/Availity/sdk-js/commit/16070049522d8db8401d71f7d5d074dc0bc213c4))
* **user-activity-broadcaster:** adding ignore because we're not using typescript EB-719 ([b66e9b8](https://github.com/Availity/sdk-js/commit/b66e9b87c2dcd305cd1ccbba5980e7550cc1bd70))
* **user-activity-broadcaster:** adding package EB-719 ([1213b58](https://github.com/Availity/sdk-js/commit/1213b58c37fcec8f5f6298e6e66103150a9db61e))



# 0.1.0-alpha.0 (2024-10-22)


### Features

* **user-activity-broadcaster:** adding changelog file EB-719 ([1607004](https://github.com/Availity/sdk-js/commit/16070049522d8db8401d71f7d5d074dc0bc213c4))
* **user-activity-broadcaster:** adding ignore because we're not using typescript EB-719 ([b66e9b8](https://github.com/Availity/sdk-js/commit/b66e9b87c2dcd305cd1ccbba5980e7550cc1bd70))
* **user-activity-broadcaster:** adding package EB-719 ([1213b58](https://github.com/Availity/sdk-js/commit/1213b58c37fcec8f5f6298e6e66103150a9db61e))
