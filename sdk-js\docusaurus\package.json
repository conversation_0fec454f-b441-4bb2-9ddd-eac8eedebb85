{"name": "@availity/dinosaurdocs", "version": "1.2.4", "private": true, "installConfig": {"hoistingLimits": "workspaces"}, "scripts": {"build": "docusaurus build", "deploy": "docusaurus deploy", "docusaurus": "<PERSON>cusaurus", "serve": "docusaurus serve", "start": "docusaurus start", "swizzle": "docusaurus swizzle", "format": "prettier '**/*' --write --ignore-unknown"}, "dependencies": {"@docusaurus/core": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@mdx-js/react": "^3.1.0", "clsx": "^2.1.1", "react": "^18.3.1", "react-dom": "^18.3.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "prettier": {"printWidth": 80, "singleQuote": true, "trailingComma": "es5", "tabWidth": 2}}