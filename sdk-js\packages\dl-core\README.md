# @availity/dl-core

> Utility to download files from services

[![Version](https://img.shields.io/npm/v/@availity/dl-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/dl-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/dl-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/dl-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/dl-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/dl-core/package.json)

## Install

### NPM

```bash
npm install @availity/api-core @availity/api-angular @availity/dl-core @availity/dl-angular
```

### Yarn

```bash
yarn add @availity/api-core @availity/api-angular @availity/dl-core @availity/dl-angular
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/api/downloads)
