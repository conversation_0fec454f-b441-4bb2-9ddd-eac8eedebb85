---
title: Introduction
slug: /
---

JavaScript client-side SDK containing different packages for interfacing with <PERSON>ility's systems.

### Supported Browsers

Packages in this repository are designed to work with the following browsers

- Google Chrome
- Microsoft Edge
- Mozilla Firefox

#### Internet Explorer Support

Active support for Internet Explorer was dropped in August 2021. As of 04/2021 we no longer provide any polyfills. These will need to be configured in your own environment.

There are 2 options available if you need support for IE 11:

- Use an older version of the package (you can check the CHANGELOG in the package to see when IE 11 support was dropped for that package)
- You can use [@availity/workflow](https://github.com/Availity/availity-workflow#readme) which will polyfill major features for you.

<!-- ---

**_If you can't find what you are looking for on any of the left sub menus try out the `search bar` at the top of every page that leverages [Algolia](https://www.algolia.com/) to provide lightning fast searches across all of our docs._** -->
