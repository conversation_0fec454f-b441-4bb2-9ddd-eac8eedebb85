# Availity JavaScript SDK

> JavaScript SDK for designed for the Availity Portal

[![Build](https://img.shields.io/github/actions/workflow/status/availity/sdk-js/deploy.yml?style=for-the-badge)](https://github.com/Availity/sdk-js/actions/workflows/deploy.yml)
[![Coverage](https://img.shields.io/codecov/c/github/Availity/sdk-js?style=for-the-badge)](https://codecov.io/gh/Availity/sdk-js)
[![License](https://img.shields.io/badge/license-MIT-blue.svg?style=for-the-badge&logo=MIT)](http://opensource.org/licenses/MIT)

## Documentation

Docs for each of the packages deployed in [sdk-js](https://github.com/Availity/sdk-js) can be found at [https://availity.github.io/](https://availity.github.io/sdk-js/)

## Supported Browsers

Packages in this repository are designed to work with the following browsers

- Google Chrome
- Microsoft Edge
- Mozilla Firefox

### Internet Explorer Support

Active support for Internet Explorer was dropped in August 2021. As of 04/2021 we no longer provide any polyfills. These will need to be configured in your own environment.

There are 2 options available if you need support for IE 11:

- Use an older version of the package (you can check the CHANGELOG in the package to see when IE 11 support was dropped for that package)
- You can use [@availity/workflow](https://github.com/Availity/availity-workflow#readme) which will polyfill major features for you.

## Contributing

Check out our [contributing guide](.github/CONTRIBUTING.md) for more information on how to contribute.

## License

[MIT](./LICENSE)
