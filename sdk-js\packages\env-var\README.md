# @availity/env-var

> Get run-time environment variables for immutable builds

[![Version](https://img.shields.io/npm/v/@availity/env-var.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/env-var)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/env-var.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/env-var)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/env-var?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/env-var/package.json)

## Install

### NPM

```bash
npm install @availity/env-var
```

### Yarn

```bash
yarn add @availity/env-var
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/env-var)
