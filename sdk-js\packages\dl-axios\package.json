{"name": "@availity/dl-axios", "version": "7.0.1", "description": "Utility to download files from services", "keywords": ["download", "file", "availity"], "homepage": "https://availity.github.io/sdk-js/api/downloads", "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/dl-axios"}, "license": "MIT", "author": "<PERSON>", "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "dependencies": {"@availity/dl-core": "workspace:*", "lodash": "^4.17.21"}, "devDependencies": {"axios": "^1.11.0", "tsup": "^8.4.0", "typescript": "^5.5.4"}, "peerDependencies": {"axios": "^1.7.7"}, "publishConfig": {"access": "public"}}