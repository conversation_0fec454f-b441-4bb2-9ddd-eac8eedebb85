{"name": "@availity/env-var", "version": "5.0.0", "description": "Availity-specific way to determine variables based on the current environment the code is running in", "keywords": ["availity", "environment", "variables"], "homepage": "https://availity.github.io/sdk-js/resources/env-var", "bugs": {"url": "https://github.com/availity/sdk-js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/availity/sdk-js.git", "directory": "packages/env-var"}, "license": "MIT", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/TheSharpieOne)"], "browser": "./dist/index.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "tsup src/index.js --format esm,cjs --dts", "dev": "tsup src/index.js --format esm,cjs --watch --dts", "lint": "eslint src", "lint:fix": "eslint src --fix", "clean": "rm -rf node_modules && rm -rf dist", "publish": "yarn npm publish --tolerate-republish --access public"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.5.4"}, "publishConfig": {"access": "public"}}