{"name": "@availity/sdk-js", "private": true, "description": "Javascript SDK for Availity", "homepage": "https://availity.github.io/sdk-js/", "bugs": {"url": "https://github.com/Availity/sdk-js/issues"}, "repository": {"type": "git", "url": "https://github.com/Availity/sdk-js.git"}, "license": "MIT", "workspaces": ["packages/*", "<PERSON>cusaurus"], "engines": {"yarn": "^3.0.0", "node": "^20.0.0 || ^22.0.0"}, "scripts": {"build": "nx run-many --target=build --all", "build:packages": "nx run-many --target=build --all --exclude=@availity/dinosaurdocs", "build:docs": "nx build @availity/dinosaurdocs", "build:affected": "nx affected --target=build", "check:deps": "adio", "check:registry": "sh ./scripts/artifactory-check.sh", "clean": "nx run-many --target=clean --all && rm -rf node_modules", "codecov:ci": "codecov", "deploy:docs": "yarn build:docs && gh-pages -d @availity/dinosaurdocs/build --message 'deployed docs [skip ci]'", "dev": "nx run-many --target=dev --all", "format:check": "nx format:check --all", "format:write": "nx format:write --all", "lint": "nx run-many --target=lint --all", "lint:affected": "nx affected --target=lint", "lint:fix": "nx affected --target=lint --fix", "new": "plop", "nx": "nx", "postinstall": "is-ci || husky", "publish": "nx affected --target version --parallel=1", "publish:dry-run": "nx affected --target version --dryRun --parallel=1", "start": "nx start @availity/dinosaurdocs", "test:affected": "nx affected --target=test", "test:ci": "nx run-many --target=test --all --parallel=3 --runInBand --coverage && node ./scripts/merge-coverage.js", "test:coverage": "nx run-many --all --target=test --parallel=3 --runInBand --coverage && node ./scripts/merge-coverage.js", "test": "nx run-many --target=test --all", "version:dry-run": "nx affected --target version --dryRun --parallel=1"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@commitlint/config-nx-scopes": "^18.6.1", "@jscutlery/semver": "^5.2.2", "@nx/eslint": "19.8.14", "@nx/jest": "19.8.14", "@nx/js": "19.8.14", "@nx/workspace": "19.8.14", "@types/jest": "^26.0.24", "@types/node": "^22.15.17", "adio": "^1.2.1", "codecov": "^3.8.3", "dependency-check": "^4.1.0", "eslint-config-availity": "^10.0.7", "gh-pages": "^6.3.0", "husky": "^9.1.7", "is-ci": "^3.0.1", "jest": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-jsdom-global": "^3.1.2", "lint-staged": "^15.5.2", "nock": "^13.5.6", "nx": "19.8.14", "plop": "^4.0.1", "prettier": "^3.5.3", "ts-jest": "^27.1.5", "typescript": "5.5.4", "undici": "^6.21.2", "xhr-mock": "^2.5.1"}, "resolutions": {"socks": "2.7.3"}, "packageManager": "yarn@3.2.0"}