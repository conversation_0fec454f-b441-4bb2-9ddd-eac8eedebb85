/**
 * Inspired By https://github.com/graphql-compose/graphql-compose-relay/blob/master/src/globalId.js
 */

export function base64(i) {
  return btoa(i);
}

export function unbase64(i) {
  return atob(i);
}

/**
 * Takes a type name and an ID specific to that type name, and returns a
 * "global ID" that is unique among all types.
 */
export function toGlobalId(type, id) {
  return base64([type, id].join(':'));
}

/**
 * Takes the "global ID" created by toGlobalID, and returns the type name and ID
 * used to create it. test change
 */
export function fromGlobalId(globalId) {
  const unbasedGlobalId = unbase64(globalId);
  const delimiterPos = unbasedGlobalId.indexOf(':');
  return {
    type: unbasedGlobalId.substring(0, delimiterPos),
    id: unbasedGlobalId.substring(delimiterPos + 1),
  };
}
