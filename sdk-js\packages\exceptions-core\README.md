# @availity/exceptions-core

> A package to catch errors in apps and logs a formatted stack trace.

[![Version](https://img.shields.io/npm/v/@availity/exceptions-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/exceptions-core)
[![NPM Downloads](https://img.shields.io/npm/dt/@availity/exceptions-core.svg?style=for-the-badge)](https://www.npmjs.com/package/@availity/exceptions-core)
[![Dependecy Status](https://img.shields.io/librariesio/release/npm/@availity/exceptions-core?style=for-the-badge)](https://github.com/Availity/sdk-js/blob/master/packages/exceptions-core/package.json)

## Install

### NPM

```bash
npm install @availity/exceptions-core
```

### Yarn

```bash
yarn add @availity/exceptions-core
```

## Documentation

Check out more documentation at [availity.github.io](https://availity.github.io/sdk-js/resources/exceptions)
